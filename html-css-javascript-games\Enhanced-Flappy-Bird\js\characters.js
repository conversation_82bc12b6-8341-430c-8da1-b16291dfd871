// 角色系统

class Character {
    constructor(type, x, y) {
        this.type = type;
        this.x = x;
        this.y = y;
        this.width = 40;
        this.height = 30;
        this.velocity = { x: 0, y: 0 };
        this.rotation = 0;
        this.scale = 1;
        
        // 从角色配置获取属性
        const config = CharacterManager.getCharacterConfig(type);
        this.jumpPower = config.jumpPower;
        this.gravity = config.gravity;
        this.maxFallSpeed = config.maxFallSpeed;
        this.abilities = { ...config.abilities };
        this.color = config.color;
        this.emoji = config.emoji;
        this.name = config.name;
        
        // 状态
        this.isAlive = true;
        this.isInvulnerable = false;
        this.invulnerabilityTime = 0;
        
        // 特殊能力冷却
        this.abilityCooldowns = {};
        Object.keys(this.abilities).forEach(ability => {
            this.abilityCooldowns[ability] = 0;
        });
        
        // 动画
        this.animationTime = 0;
        this.flapAnimation = 0;
        
        // 轨迹记录
        this.trail = [];
        this.maxTrailLength = 10;
    }
    
    update(deltaTime) {
        if (!this.isAlive) return;
        
        // 更新动画时间
        this.animationTime += deltaTime;
        this.flapAnimation = Math.sin(this.animationTime * 0.01) * 0.2;
        
        // 应用重力
        this.velocity.y += this.gravity * deltaTime * 0.001;
        
        // 限制下降速度
        if (this.velocity.y > this.maxFallSpeed) {
            this.velocity.y = this.maxFallSpeed;
        }
        
        // 更新位置
        this.x += this.velocity.x * deltaTime * 0.001;
        this.y += this.velocity.y * deltaTime * 0.001;
        
        // 更新旋转（根据速度）
        this.rotation = MathUtils.clamp(this.velocity.y * 0.05, -0.5, 1.5);
        
        // 更新无敌时间
        if (this.isInvulnerable) {
            this.invulnerabilityTime -= deltaTime;
            if (this.invulnerabilityTime <= 0) {
                this.isInvulnerable = false;
            }
        }
        
        // 更新能力冷却
        Object.keys(this.abilityCooldowns).forEach(ability => {
            if (this.abilityCooldowns[ability] > 0) {
                this.abilityCooldowns[ability] -= deltaTime;
            }
        });
        
        // 更新轨迹
        this.trail.push({ x: this.x, y: this.y, time: Date.now() });
        if (this.trail.length > this.maxTrailLength) {
            this.trail.shift();
        }
        
        // 特殊能力效果
        this.updateAbilityEffects(deltaTime);
    }
    
    updateAbilityEffects(deltaTime) {
        // 磁铁效果
        if (this.abilities.magnetism && this.abilityCooldowns.magnetism <= 0) {
            // 这里会在游戏主循环中处理磁铁效果
        }
        
        // 彩虹轨迹
        if (this.abilities.rainbowTrail) {
            if (Math.random() < 0.3) {
                window.particleSystem?.createRainbowTrail(
                    this.x + this.width / 2,
                    this.y + this.height / 2
                );
            }
        }
    }
    
    jump() {
        if (!this.isAlive) return;
        
        // 基础跳跃
        this.velocity.y = -this.jumpPower;
        
        // 双跳能力
        if (this.abilities.doubleJump && this.abilityCooldowns.doubleJump <= 0) {
            this.velocity.y *= 1.5;
            this.abilityCooldowns.doubleJump = 1000; // 1秒冷却
            
            // 特效
            window.particleSystem?.createExplosion(
                this.x + this.width / 2,
                this.y + this.height / 2,
                this.color
            );
        }
        
        // 播放音效
        window.audioManager?.playSound('jump');
        
        // 发射事件
        window.gameEvents?.emit('playerJump', this);
    }
    
    useAbility(abilityName) {
        if (!this.abilities[abilityName] || this.abilityCooldowns[abilityName] > 0) {
            return false;
        }
        
        switch (abilityName) {
            case 'shield':
                this.activateShield();
                break;
            case 'teleport':
                this.teleport();
                break;
            case 'slowMotion':
                this.activateSlowMotion();
                break;
            case 'magnetism':
                this.activateMagnetism();
                break;
        }
        
        return true;
    }
    
    activateShield() {
        this.isInvulnerable = true;
        this.invulnerabilityTime = 3000; // 3秒无敌
        this.abilityCooldowns.shield = 10000; // 10秒冷却
        
        // 护盾特效
        window.particleSystem?.createShieldEffect(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        
        window.gameEvents?.emit('shieldActivated', this);
    }
    
    teleport() {
        // 传送到安全位置
        this.y = MathUtils.clamp(this.y - 100, 50, window.gameCanvas.height - 150);
        this.abilityCooldowns.teleport = 5000; // 5秒冷却
        
        // 传送特效
        window.particleSystem?.createExplosion(
            this.x + this.width / 2,
            this.y + this.height / 2,
            '#9B59B6'
        );
        
        window.gameEvents?.emit('playerTeleported', this);
    }
    
    activateSlowMotion() {
        this.abilityCooldowns.slowMotion = 15000; // 15秒冷却
        window.gameEvents?.emit('slowMotionActivated', 3000); // 3秒慢动作
    }
    
    activateMagnetism() {
        this.abilityCooldowns.magnetism = 8000; // 8秒冷却
        window.gameEvents?.emit('magnetismActivated', 5000); // 5秒磁铁效果
    }
    
    takeDamage() {
        if (this.isInvulnerable) return false;
        
        this.isAlive = false;
        
        // 死亡特效
        window.particleSystem?.createExplosion(
            this.x + this.width / 2,
            this.y + this.height / 2,
            '#FF6B6B'
        );
        
        window.gameEvents?.emit('playerDied', this);
        return true;
    }
    
    draw(ctx) {
        ctx.save();
        
        // 无敌闪烁效果
        if (this.isInvulnerable) {
            ctx.globalAlpha = Math.sin(Date.now() * 0.01) * 0.5 + 0.5;
        }
        
        // 绘制轨迹
        if (this.abilities.rainbowTrail && this.trail.length > 1) {
            ctx.strokeStyle = ColorUtils.rainbowColor(Date.now());
            ctx.lineWidth = 3;
            ctx.globalAlpha = 0.6;
            ctx.beginPath();
            for (let i = 0; i < this.trail.length; i++) {
                const point = this.trail[i];
                if (i === 0) {
                    ctx.moveTo(point.x + this.width / 2, point.y + this.height / 2);
                } else {
                    ctx.lineTo(point.x + this.width / 2, point.y + this.height / 2);
                }
            }
            ctx.stroke();
        }
        
        // 绘制角色
        ctx.translate(this.x + this.width / 2, this.y + this.height / 2);
        ctx.rotate(this.rotation);
        ctx.scale(this.scale + this.flapAnimation, this.scale);
        
        // 绘制角色主体
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.ellipse(0, 0, this.width / 2, this.height / 2, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制眼睛
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.ellipse(-8, -5, 6, 8, 0, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.fillStyle = '#000000';
        ctx.beginPath();
        ctx.ellipse(-6, -3, 3, 4, 0, 0, Math.PI * 2);
        ctx.fill();
        
        // 绘制嘴巴
        ctx.fillStyle = '#FFA500';
        ctx.beginPath();
        ctx.moveTo(8, 0);
        ctx.lineTo(18, -3);
        ctx.lineTo(18, 3);
        ctx.closePath();
        ctx.fill();
        
        // 绘制翅膀
        ctx.fillStyle = this.color;
        ctx.globalAlpha = 0.8;
        ctx.beginPath();
        ctx.ellipse(-15, 0, 8, 15, this.flapAnimation, 0, Math.PI * 2);
        ctx.fill();
        
        ctx.restore();
        
        // 绘制能力指示器
        this.drawAbilityIndicators(ctx);
    }
    
    drawAbilityIndicators(ctx) {
        let indicatorY = this.y - 20;
        
        // 护盾指示器
        if (this.isInvulnerable) {
            ctx.fillStyle = '#45B7D1';
            ctx.font = '16px Arial';
            ctx.fillText('🛡️', this.x + this.width / 2 - 8, indicatorY);
            indicatorY -= 20;
        }
        
        // 其他能力指示器可以在这里添加
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
    
    reset(x, y) {
        this.x = x;
        this.y = y;
        this.velocity = { x: 0, y: 0 };
        this.rotation = 0;
        this.isAlive = true;
        this.isInvulnerable = false;
        this.invulnerabilityTime = 0;
        this.trail = [];
        
        // 重置冷却时间
        Object.keys(this.abilityCooldowns).forEach(ability => {
            this.abilityCooldowns[ability] = 0;
        });
    }
}

// 角色管理器
class CharacterManager {
    static characters = {
        classic: {
            name: '经典鸟',
            emoji: '🐦',
            color: '#FFD700',
            jumpPower: 300,
            gravity: 800,
            maxFallSpeed: 400,
            abilities: {},
            unlocked: true,
            description: '经典的Flappy Bird，平衡的属性'
        },
        
        rocket: {
            name: '火箭鸟',
            emoji: '🚀',
            color: '#FF6B6B',
            jumpPower: 400,
            gravity: 600,
            maxFallSpeed: 500,
            abilities: { doubleJump: true },
            unlocked: false,
            unlockCondition: { type: 'score', value: 10 },
            description: '强力跳跃，可以双跳'
        },
        
        ninja: {
            name: '忍者鸟',
            emoji: '🥷',
            color: '#2C3E50',
            jumpPower: 250,
            gravity: 700,
            maxFallSpeed: 350,
            abilities: { teleport: true, shield: true },
            unlocked: false,
            unlockCondition: { type: 'score', value: 25 },
            description: '可以传送和使用护盾'
        },
        
        rainbow: {
            name: '彩虹鸟',
            emoji: '🌈',
            color: '#9B59B6',
            jumpPower: 280,
            gravity: 750,
            maxFallSpeed: 380,
            abilities: { rainbowTrail: true, magnetism: true },
            unlocked: false,
            unlockCondition: { type: 'score', value: 50 },
            description: '彩虹轨迹和磁铁效果'
        },
        
        phoenix: {
            name: '凤凰',
            emoji: '🔥',
            color: '#E74C3C',
            jumpPower: 350,
            gravity: 650,
            maxFallSpeed: 450,
            abilities: { shield: true, slowMotion: true, doubleJump: true },
            unlocked: false,
            unlockCondition: { type: 'level', value: 5 },
            description: '传说中的凤凰，拥有多种能力'
        }
    };
    
    static getCharacterConfig(type) {
        return this.characters[type] || this.characters.classic;
    }
    
    static getAllCharacters() {
        return Object.keys(this.characters).map(key => ({
            type: key,
            ...this.characters[key]
        }));
    }
    
    static isCharacterUnlocked(type, playerStats) {
        const character = this.characters[type];
        if (!character || character.unlocked) return true;
        
        const condition = character.unlockCondition;
        if (!condition) return true;
        
        switch (condition.type) {
            case 'score':
                return playerStats.highScore >= condition.value;
            case 'level':
                return playerStats.level >= condition.value;
            case 'achievement':
                return playerStats.achievements.includes(condition.value);
            default:
                return false;
        }
    }
    
    static unlockCharacter(type) {
        if (this.characters[type]) {
            this.characters[type].unlocked = true;
        }
    }
}
