/* General <PERSON> */
body {
  background-image: url('https://raw.githubusercontent.com/arpit456jain/Amazing-Js-Projects/master/Candy%20Crush/utils/bg.png');
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  font-family: 'Montserrat', sans-serif;
  color: #85796b;
}

.grid {
  display: flex;
  flex-wrap: wrap;
  height: 560px;
  width: 560px;
  background-color: rgba(109, 127, 151, 0.5);
  padding: 5px;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) inset, 0 1px 0 #fff;
  margin-left: 80px;
  margin-top: 50px;
}

.grid div {
  height: 70px;
  width: 70px;
  background-size: cover;
  background-position: center;
  border-radius: 5px;
  transition: transform 0.2s ease; /* Smooth animation for interactions */
}

/* Hover effect for interactivity */
.grid div:hover {
    transform: scale(1.05); /* Slightly enlarge on hover */
}

/* Scoreboard Styling */
.scoreBoard {
  background-color: cyan;
  border-radius: 20px;
  margin-top: 200px;
  margin-left: 200px;
  width: auto;
  height: 120px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  color: #85796b;
}

h3, h1 {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  margin: 0;
}

h1 {
  margin-top: -10px;
}

.grid div:hover {
    transform: scale(1.05); /* Slightly enlarge on hover */
}

/* Mode Selection Styling */
#modeSelection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f0f0f0;
  font-family: 'Montserrat', sans-serif;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
}

#modeSelection h2 {
  margin-bottom: 20px;
  color: #333;
}

#modeSelection button {
  margin: 10px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  background-color: #87ceeb;
  border: none;
  border-radius: 5px;
  color: white;
}

/* Timer and Change Mode Button */
#timer {
  font-size: 18px;
  margin-top: 10px;
}

#changeMode {
  margin-top: 10px;
  padding: 5px 10px;
  background-color: #ff6347;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

/* Initially Hide Game Elements */
.grid, .scoreBoard {
  display: none;
}