// 工具函数集合

// 数学工具
const MathUtils = {
    // 生成随机数
    random: (min, max) => Math.random() * (max - min) + min,
    
    // 生成随机整数
    randomInt: (min, max) => Math.floor(Math.random() * (max - min + 1)) + min,
    
    // 限制数值范围
    clamp: (value, min, max) => Math.min(Math.max(value, min), max),
    
    // 线性插值
    lerp: (start, end, factor) => start + (end - start) * factor,
    
    // 距离计算
    distance: (x1, y1, x2, y2) => Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2),
    
    // 角度转弧度
    toRadians: (degrees) => degrees * Math.PI / 180,
    
    // 弧度转角度
    toDegrees: (radians) => radians * 180 / Math.PI
};

// 颜色工具
const ColorUtils = {
    // HSL转RGB
    hslToRgb: (h, s, l) => {
        h /= 360;
        s /= 100;
        l /= 100;
        
        const hue2rgb = (p, q, t) => {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        };
        
        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;
        
        const r = Math.round(hue2rgb(p, q, h + 1/3) * 255);
        const g = Math.round(hue2rgb(p, q, h) * 255);
        const b = Math.round(hue2rgb(p, q, h - 1/3) * 255);
        
        return `rgb(${r}, ${g}, ${b})`;
    },
    
    // 随机颜色
    randomColor: () => {
        const hue = MathUtils.randomInt(0, 360);
        const saturation = MathUtils.randomInt(50, 100);
        const lightness = MathUtils.randomInt(40, 80);
        return ColorUtils.hslToRgb(hue, saturation, lightness);
    },
    
    // 彩虹色
    rainbowColor: (time) => {
        const hue = (time * 50) % 360;
        return ColorUtils.hslToRgb(hue, 100, 60);
    }
};

// 动画工具
const AnimationUtils = {
    // 缓动函数
    easing: {
        linear: t => t,
        easeInQuad: t => t * t,
        easeOutQuad: t => t * (2 - t),
        easeInOutQuad: t => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t,
        easeInCubic: t => t * t * t,
        easeOutCubic: t => (--t) * t * t + 1,
        easeInOutCubic: t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1,
        bounce: t => {
            if (t < 1/2.75) return 7.5625 * t * t;
            if (t < 2/2.75) return 7.5625 * (t -= 1.5/2.75) * t + 0.75;
            if (t < 2.5/2.75) return 7.5625 * (t -= 2.25/2.75) * t + 0.9375;
            return 7.5625 * (t -= 2.625/2.75) * t + 0.984375;
        }
    },
    
    // 创建动画
    animate: (duration, callback, easing = 'linear') => {
        const start = performance.now();
        const easingFunc = AnimationUtils.easing[easing] || AnimationUtils.easing.linear;
        
        const frame = (time) => {
            const elapsed = time - start;
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = easingFunc(progress);
            
            callback(easedProgress);
            
            if (progress < 1) {
                requestAnimationFrame(frame);
            }
        };
        
        requestAnimationFrame(frame);
    }
};

// 碰撞检测
const CollisionUtils = {
    // 矩形碰撞检测
    rectRect: (rect1, rect2) => {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    },
    
    // 圆形碰撞检测
    circleCircle: (circle1, circle2) => {
        const distance = MathUtils.distance(circle1.x, circle1.y, circle2.x, circle2.y);
        return distance < circle1.radius + circle2.radius;
    },
    
    // 点在矩形内
    pointInRect: (point, rect) => {
        return point.x >= rect.x &&
               point.x <= rect.x + rect.width &&
               point.y >= rect.y &&
               point.y <= rect.y + rect.height;
    },
    
    // 点在圆内
    pointInCircle: (point, circle) => {
        const distance = MathUtils.distance(point.x, point.y, circle.x, circle.y);
        return distance <= circle.radius;
    }
};

// 本地存储工具
const StorageUtils = {
    // 保存数据
    save: (key, data) => {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (e) {
            console.error('保存数据失败:', e);
            return false;
        }
    },
    
    // 加载数据
    load: (key, defaultValue = null) => {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : defaultValue;
        } catch (e) {
            console.error('加载数据失败:', e);
            return defaultValue;
        }
    },
    
    // 删除数据
    remove: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.error('删除数据失败:', e);
            return false;
        }
    }
};

// DOM工具
const DOMUtils = {
    // 获取元素
    get: (selector) => document.querySelector(selector),
    
    // 获取所有元素
    getAll: (selector) => document.querySelectorAll(selector),
    
    // 添加类
    addClass: (element, className) => {
        if (element) element.classList.add(className);
    },
    
    // 移除类
    removeClass: (element, className) => {
        if (element) element.classList.remove(className);
    },
    
    // 切换类
    toggleClass: (element, className) => {
        if (element) element.classList.toggle(className);
    },
    
    // 显示元素
    show: (selector) => {
        const element = typeof selector === 'string' ? DOMUtils.get(selector) : selector;
        if (element) element.classList.remove('hidden');
    },

    // 隐藏元素
    hide: (selector) => {
        const element = typeof selector === 'string' ? DOMUtils.get(selector) : selector;
        if (element) element.classList.add('hidden');
    },
    
    // 创建元素
    create: (tag, className = '', innerHTML = '') => {
        const element = document.createElement(tag);
        if (className) element.className = className;
        if (innerHTML) element.innerHTML = innerHTML;
        return element;
    }
};

// 时间工具
const TimeUtils = {
    // 格式化时间
    formatTime: (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    },
    
    // 获取时间戳
    now: () => Date.now(),
    
    // 延迟执行
    delay: (ms) => new Promise(resolve => setTimeout(resolve, ms))
};

// 事件发射器
class EventEmitter {
    constructor() {
        this.events = {};
    }
    
    on(event, callback) {
        if (!this.events[event]) {
            this.events[event] = [];
        }
        this.events[event].push(callback);
    }
    
    off(event, callback) {
        if (this.events[event]) {
            this.events[event] = this.events[event].filter(cb => cb !== callback);
        }
    }
    
    emit(event, ...args) {
        if (this.events[event]) {
            this.events[event].forEach(callback => callback(...args));
        }
    }
}

// 导出全局事件发射器
window.gameEvents = new EventEmitter();
