// 渲染系统

class Renderer {
    constructor(canvas, game) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.game = game;
        this.width = canvas.width;
        this.height = canvas.height;
        
        // 渲染设置
        this.showDebug = false;
        this.showParticles = true;
        this.showTrails = true;
        
        // 背景设置
        this.backgroundGradient = this.createBackgroundGradient();
        this.cloudPositions = this.generateClouds();
        
        // UI元素缓存
        this.uiElements = new Map();
        
        // 性能监控
        this.frameCount = 0;
        this.lastFpsTime = 0;
        this.fps = 0;
    }
    
    createBackgroundGradient() {
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.height);
        gradient.addColorStop(0, '#87CEEB'); // 天空蓝
        gradient.addColorStop(0.7, '#98D8E8'); // 浅蓝
        gradient.addColorStop(1, '#F0F8FF'); // 白色
        return gradient;
    }
    
    generateClouds() {
        const clouds = [];
        for (let i = 0; i < 8; i++) {
            clouds.push({
                x: MathUtils.random(0, this.width * 2),
                y: MathUtils.random(50, this.height * 0.4),
                size: MathUtils.random(40, 80),
                speed: MathUtils.random(10, 30),
                opacity: MathUtils.random(0.3, 0.7)
            });
        }
        return clouds;
    }
    
    render(currentTime) {
        // 清空画布
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // 绘制背景
        this.drawBackground();
        
        // 根据游戏状态渲染
        switch (this.game.state) {
            case 'menu':
                this.renderMenu();
                break;
            case 'playing':
                this.renderGame();
                break;
            case 'paused':
                this.renderGame();
                this.renderPauseOverlay();
                break;
            case 'gameOver':
                this.renderGame();
                this.renderGameOverOverlay();
                break;
        }
        
        // 绘制调试信息
        if (this.showDebug) {
            this.drawDebugInfo(currentTime);
        }
        
        // 更新FPS
        this.updateFPS(currentTime);
    }
    
    drawBackground() {
        // 绘制天空渐变
        this.ctx.fillStyle = this.backgroundGradient;
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        // 绘制云朵
        this.drawClouds();
        
        // 绘制地面
        this.drawGround();
    }
    
    drawClouds() {
        this.cloudPositions.forEach(cloud => {
            this.ctx.save();
            this.ctx.globalAlpha = cloud.opacity;
            this.ctx.fillStyle = '#FFFFFF';
            
            // 移动云朵
            cloud.x -= cloud.speed * 0.016; // 假设60fps
            if (cloud.x + cloud.size < 0) {
                cloud.x = this.width + cloud.size;
            }
            
            // 绘制云朵（简单的圆形组合）
            this.drawCloud(cloud.x, cloud.y, cloud.size);
            this.ctx.restore();
        });
    }
    
    drawCloud(x, y, size) {
        const ctx = this.ctx;
        ctx.beginPath();
        ctx.arc(x, y, size * 0.5, 0, Math.PI * 2);
        ctx.arc(x + size * 0.3, y, size * 0.4, 0, Math.PI * 2);
        ctx.arc(x - size * 0.3, y, size * 0.4, 0, Math.PI * 2);
        ctx.arc(x + size * 0.15, y - size * 0.3, size * 0.35, 0, Math.PI * 2);
        ctx.arc(x - size * 0.15, y - size * 0.3, size * 0.35, 0, Math.PI * 2);
        ctx.fill();
    }
    
    drawGround() {
        const groundHeight = 50;
        const gradient = this.ctx.createLinearGradient(0, this.height - groundHeight, 0, this.height);
        gradient.addColorStop(0, '#8FBC8F');
        gradient.addColorStop(1, '#228B22');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, this.height - groundHeight, this.width, groundHeight);
        
        // 绘制草地纹理
        this.ctx.fillStyle = '#32CD32';
        for (let x = 0; x < this.width; x += 20) {
            const grassHeight = MathUtils.random(5, 15);
            this.ctx.fillRect(x, this.height - groundHeight, 2, -grassHeight);
        }
    }
    
    renderMenu() {
        // 背景粒子效果
        if (this.game.backgroundParticles) {
            this.game.backgroundParticles.draw(this.ctx);
        }
        
        // 标题文字已在HTML中处理
        // 这里可以添加额外的菜单动画效果
        this.drawMenuParticles();
    }
    
    drawMenuParticles() {
        // 绘制飘浮的星星
        const time = Date.now() * 0.001;
        for (let i = 0; i < 20; i++) {
            const x = (i * 50 + Math.sin(time + i) * 30) % this.width;
            const y = (i * 30 + Math.cos(time + i * 0.5) * 20) % this.height;
            const size = 2 + Math.sin(time * 2 + i) * 1;
            
            this.ctx.fillStyle = `hsl(${(time * 50 + i * 30) % 360}, 70%, 70%)`;
            this.ctx.beginPath();
            this.drawStar(x, y, size);
            this.ctx.fill();
        }
    }
    
    drawStar(x, y, size) {
        const ctx = this.ctx;
        ctx.save();
        ctx.translate(x, y);
        ctx.beginPath();
        for (let i = 0; i < 5; i++) {
            ctx.lineTo(Math.cos((i * 4 * Math.PI) / 5) * size, Math.sin((i * 4 * Math.PI) / 5) * size);
        }
        ctx.closePath();
        ctx.restore();
    }
    
    renderGame() {
        // 绘制管道
        this.drawPipes();
        
        // 绘制金币
        this.drawCoins();
        
        // 绘制道具
        this.drawPowerUps();
        
        // 绘制玩家
        if (this.game.player) {
            this.game.player.draw(this.ctx);
        }
        
        // 绘制粒子效果
        if (this.showParticles && this.game.particleSystem) {
            this.game.particleSystem.draw(this.ctx);
        }
        
        // 绘制UI
        this.drawGameUI();
        
        // 绘制特效覆盖层
        this.drawEffectOverlays();
    }
    
    drawPipes() {
        this.game.pipes.forEach(pipe => {
            // 管道主体
            const gradient = this.ctx.createLinearGradient(pipe.x, 0, pipe.x + pipe.width, 0);
            gradient.addColorStop(0, '#228B22');
            gradient.addColorStop(0.5, '#32CD32');
            gradient.addColorStop(1, '#228B22');
            
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(pipe.x, pipe.y, pipe.width, pipe.height);
            
            // 管道边框
            this.ctx.strokeStyle = '#006400';
            this.ctx.lineWidth = 3;
            this.ctx.strokeRect(pipe.x, pipe.y, pipe.width, pipe.height);
            
            // 管道口
            if (pipe.type === 'top') {
                this.ctx.fillRect(pipe.x - 5, pipe.height - 30, pipe.width + 10, 30);
                this.ctx.strokeRect(pipe.x - 5, pipe.height - 30, pipe.width + 10, 30);
            } else {
                this.ctx.fillRect(pipe.x - 5, pipe.y, pipe.width + 10, 30);
                this.ctx.strokeRect(pipe.x - 5, pipe.y, pipe.width + 10, 30);
            }
        });
    }
    
    drawCoins() {
        this.game.coins.forEach(coin => {
            if (!coin.collected) {
                coin.draw(this.ctx);
            }
        });
    }
    
    drawPowerUps() {
        this.game.powerUps.forEach(powerUp => {
            if (!powerUp.collected) {
                powerUp.draw(this.ctx);
            }
        });
    }
    
    drawGameUI() {
        // 分数显示
        this.ctx.save();
        this.ctx.font = 'bold 36px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 3;
        this.ctx.textAlign = 'center';
        
        const scoreText = this.game.score.toString();
        this.ctx.strokeText(scoreText, this.width / 2, 60);
        this.ctx.fillText(scoreText, this.width / 2, 60);
        
        // 等级显示
        this.ctx.font = 'bold 20px Arial';
        const levelText = `Level ${this.game.level}`;
        this.ctx.strokeText(levelText, this.width / 2, 90);
        this.ctx.fillText(levelText, this.width / 2, 90);
        
        // 经验条
        this.drawExperienceBar();
        
        // 金币显示
        this.ctx.font = 'bold 18px Arial';
        this.ctx.textAlign = 'left';
        const coinText = `💰 ${this.game.coins}`;
        this.ctx.strokeText(coinText, 20, 30);
        this.ctx.fillText(coinText, 20, 30);
        
        this.ctx.restore();
    }
    
    drawExperienceBar() {
        const barWidth = 200;
        const barHeight = 8;
        const x = (this.width - barWidth) / 2;
        const y = 100;
        
        // 背景
        this.ctx.fillStyle = '#333333';
        this.ctx.fillRect(x, y, barWidth, barHeight);
        
        // 经验条
        const progress = this.game.experience / this.game.experienceToNext;
        this.ctx.fillStyle = '#FFD700';
        this.ctx.fillRect(x, y, barWidth * progress, barHeight);
        
        // 边框
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(x, y, barWidth, barHeight);
    }
    
    drawEffectOverlays() {
        // 慢动作效果
        if (this.game.slowMotionTime > 0) {
            this.ctx.save();
            this.ctx.fillStyle = 'rgba(0, 100, 255, 0.1)';
            this.ctx.fillRect(0, 0, this.width, this.height);
            this.ctx.restore();
        }
        
        // 双倍得分效果
        if (this.game.doubleScoreTime > 0) {
            this.ctx.save();
            this.ctx.fillStyle = 'rgba(255, 215, 0, 0.1)';
            this.ctx.fillRect(0, 0, this.width, this.height);
            this.ctx.restore();
        }
        
        // 磁铁效果
        if (this.game.magnetismTime > 0) {
            this.drawMagnetismEffect();
        }
    }
    
    drawMagnetismEffect() {
        if (!this.game.player) return;
        
        const centerX = this.game.player.x + this.game.player.width / 2;
        const centerY = this.game.player.y + this.game.player.height / 2;
        const radius = 100 + Math.sin(Date.now() * 0.01) * 20;
        
        this.ctx.save();
        this.ctx.strokeStyle = '#4ECDC4';
        this.ctx.lineWidth = 3;
        this.ctx.globalAlpha = 0.6;
        this.ctx.setLineDash([10, 5]);
        this.ctx.beginPath();
        this.ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
        this.ctx.stroke();
        this.ctx.restore();
    }
    
    renderPauseOverlay() {
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        this.ctx.font = 'bold 48px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('PAUSED', this.width / 2, this.height / 2);
        
        this.ctx.font = '24px Arial';
        this.ctx.fillText('Press SPACE to resume', this.width / 2, this.height / 2 + 50);
        this.ctx.restore();
    }
    
    renderGameOverOverlay() {
        this.ctx.save();
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.ctx.fillRect(0, 0, this.width, this.height);
        
        this.ctx.font = 'bold 48px Arial';
        this.ctx.fillStyle = '#FF6B6B';
        this.ctx.textAlign = 'center';
        this.ctx.fillText('GAME OVER', this.width / 2, this.height / 2 - 50);
        
        this.ctx.font = '24px Arial';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.fillText(`Final Score: ${this.game.score}`, this.width / 2, this.height / 2);
        this.ctx.fillText('Press R to restart', this.width / 2, this.height / 2 + 50);
        this.ctx.restore();
    }
    
    drawDebugInfo(currentTime) {
        this.ctx.save();
        this.ctx.font = '14px monospace';
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 1;
        
        const debugInfo = [
            `FPS: ${this.fps}`,
            `State: ${this.game.state}`,
            `Score: ${this.game.score}`,
            `Pipes: ${this.game.pipes.length}`,
            `PowerUps: ${this.game.powerUps.length}`,
            `Coins: ${this.game.coins.length}`,
            `Particles: ${this.game.particleSystem?.particles.length || 0}`
        ];
        
        debugInfo.forEach((info, index) => {
            const y = 20 + index * 20;
            this.ctx.strokeText(info, 10, y);
            this.ctx.fillText(info, 10, y);
        });
        
        this.ctx.restore();
    }
    
    updateFPS(currentTime) {
        this.frameCount++;
        if (currentTime - this.lastFpsTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastFpsTime));
            this.frameCount = 0;
            this.lastFpsTime = currentTime;
        }
    }
    
    toggleDebug() {
        this.showDebug = !this.showDebug;
    }
    
    toggleParticles() {
        this.showParticles = !this.showParticles;
    }
    
    toggleTrails() {
        this.showTrails = !this.showTrails;
    }
    
    resize(width, height) {
        this.width = width;
        this.height = height;
        this.canvas.width = width;
        this.canvas.height = height;
        
        // 重新创建背景渐变
        this.backgroundGradient = this.createBackgroundGradient();
    }
}
