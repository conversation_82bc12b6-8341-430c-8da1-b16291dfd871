/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameContainer {
    position: relative;
    width: 800px;
    height: 600px;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* 画布样式 */
#gameCanvas {
    position: absolute;
    top: 0;
    left: 0;
    background: linear-gradient(to bottom, #87CEEB 0%, #98FB98 100%);
    border-radius: 20px;
}

/* 屏幕通用样式 */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    z-index: 10;
}

.screen.hidden {
    display: none;
}

.menu-content {
    text-align: center;
    color: white;
    max-width: 400px;
    padding: 40px;
}

/* 游戏标题 */
.game-title {
    font-family: 'Orbitron', monospace;
    font-size: 3em;
    font-weight: 900;
    margin-bottom: 30px;
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF6347);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
    animation: titleGlow 2s ease-in-out infinite alternate;
}

@keyframes titleGlow {
    from { text-shadow: 0 0 30px rgba(255, 215, 0, 0.5); }
    to { text-shadow: 0 0 50px rgba(255, 215, 0, 0.8); }
}

/* 按钮样式 */
.menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 30px 0;
}

.menu-btn {
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.menu-btn.primary {
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    border: none;
    font-weight: 700;
}

.menu-btn.primary:hover {
    background: linear-gradient(45deg, #FF5252, #26C6DA);
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

/* 统计预览 */
.stats-preview {
    display: flex;
    justify-content: space-around;
    margin-top: 20px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    font-size: 0.9em;
    opacity: 0.8;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.5em;
    font-weight: 700;
    color: #FFD700;
}

/* 角色选择网格 */
.character-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin: 30px 0;
}

.character-card {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.character-card:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.character-card.selected {
    border-color: #FFD700;
    background: rgba(255, 215, 0, 0.2);
}

.character-icon {
    font-size: 3em;
    margin-bottom: 10px;
}

.character-name {
    font-weight: 700;
    margin-bottom: 5px;
}

.character-ability {
    font-size: 0.9em;
    opacity: 0.8;
}

/* 游戏UI */
#gameUI {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 5;
}

#gameUI > * {
    pointer-events: auto;
}

.top-ui {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.score-display {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 1.5em;
    font-weight: 700;
    font-family: 'Orbitron', monospace;
}

.level-display {
    background: rgba(255, 215, 0, 0.9);
    color: black;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 700;
}

.exp-bar {
    flex: 1;
    height: 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 5px;
    margin: 0 15px;
    overflow: hidden;
}

.exp-fill {
    height: 100%;
    background: linear-gradient(90deg, #4ECDC4, #44A08D);
    border-radius: 5px;
    transition: width 0.3s ease;
    width: 0%;
}

/* 道具显示 */
.power-ups {
    position: absolute;
    top: 80px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.power-up {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: powerUpPulse 1s ease-in-out infinite alternate;
}

@keyframes powerUpPulse {
    from { transform: scale(1); }
    to { transform: scale(1.05); }
}

.power-up-icon {
    font-size: 1.5em;
}

.power-up-timer {
    font-weight: 700;
    font-family: 'Orbitron', monospace;
}

/* 暂停按钮 */
.pause-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.2em;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pause-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
}

/* 游戏结束样式 */
.game-over-title {
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #FF6B6B;
}

.final-stats {
    display: flex;
    justify-content: space-around;
    margin: 30px 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
}

/* 成就系统 */
.achievements-container {
    max-height: 400px;
    overflow-y: auto;
    padding: 20px;
}

.achievement-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.achievement-item.unlocked {
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #FFD700;
}

.achievement-icon {
    font-size: 2em;
}

.achievement-info h4 {
    margin-bottom: 5px;
}

.achievement-info p {
    opacity: 0.8;
    font-size: 0.9em;
}

/* 设置界面 */
.settings-options {
    text-align: left;
    margin: 30px 0;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

.setting-item label {
    font-weight: 500;
}

.setting-item input[type="range"] {
    width: 150px;
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    padding: 5px 10px;
}

/* 粒子效果 */
#particleContainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 900px) {
    #gameContainer {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }
    
    #gameCanvas {
        border-radius: 0;
    }
    
    .screen {
        border-radius: 0;
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 动画类 */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}
