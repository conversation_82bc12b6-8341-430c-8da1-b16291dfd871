// 音频管理系统

class AudioManager {
    constructor() {
        this.sounds = new Map();
        this.music = new Map();
        this.soundVolume = 0.7;
        this.musicVolume = 0.5;
        this.muted = false;
        this.currentMusic = null;
        this.audioContext = null;
        this.masterGain = null;
        
        this.initializeAudioContext();
        this.loadSettings();
        this.createSounds();
    }
    
    initializeAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
        } catch (e) {
            console.warn('Web Audio API not supported, falling back to HTML5 audio');
        }
    }
    
    createSounds() {
        // 使用Web Audio API创建程序化音效
        this.createJumpSound();
        this.createScoreSound();
        this.createPowerUpSound();
        this.createCoinSound();
        this.createGameOverSound();
        this.createAchievementSound();
        this.createBackgroundMusic();
    }
    
    createJumpSound() {
        this.sounds.set('jump', () => {
            if (!this.audioContext) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.type = 'square';
            oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(200, this.audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(this.soundVolume * 0.3, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.1);
        });
    }
    
    createScoreSound() {
        this.sounds.set('score', () => {
            if (!this.audioContext) return;
            
            const oscillator1 = this.audioContext.createOscillator();
            const oscillator2 = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator1.connect(gainNode);
            oscillator2.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator1.type = 'sine';
            oscillator2.type = 'sine';
            
            oscillator1.frequency.setValueAtTime(523, this.audioContext.currentTime); // C5
            oscillator1.frequency.setValueAtTime(659, this.audioContext.currentTime + 0.1); // E5
            oscillator1.frequency.setValueAtTime(784, this.audioContext.currentTime + 0.2); // G5
            
            oscillator2.frequency.setValueAtTime(262, this.audioContext.currentTime); // C4
            oscillator2.frequency.setValueAtTime(330, this.audioContext.currentTime + 0.1); // E4
            oscillator2.frequency.setValueAtTime(392, this.audioContext.currentTime + 0.2); // G4
            
            gainNode.gain.setValueAtTime(this.soundVolume * 0.4, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
            
            oscillator1.start(this.audioContext.currentTime);
            oscillator2.start(this.audioContext.currentTime);
            oscillator1.stop(this.audioContext.currentTime + 0.3);
            oscillator2.stop(this.audioContext.currentTime + 0.3);
        });
    }
    
    createPowerUpSound() {
        this.sounds.set('powerup', () => {
            if (!this.audioContext) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();
            
            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.type = 'sawtooth';
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(1000, this.audioContext.currentTime);
            filter.frequency.exponentialRampToValueAtTime(3000, this.audioContext.currentTime + 0.3);
            
            oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(800, this.audioContext.currentTime + 0.3);
            
            gainNode.gain.setValueAtTime(this.soundVolume * 0.5, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.3);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.3);
        });
    }
    
    createCoinSound() {
        this.sounds.set('coin', () => {
            if (!this.audioContext) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(1200, this.audioContext.currentTime + 0.1);
            
            gainNode.gain.setValueAtTime(this.soundVolume * 0.3, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.1);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.1);
        });
    }
    
    createGameOverSound() {
        this.sounds.set('gameover', () => {
            if (!this.audioContext) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.type = 'triangle';
            oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(100, this.audioContext.currentTime + 0.5);
            
            gainNode.gain.setValueAtTime(this.soundVolume * 0.6, this.audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + 0.5);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + 0.5);
        });
    }
    
    createAchievementSound() {
        this.sounds.set('achievement', () => {
            if (!this.audioContext) return;
            
            const notes = [523, 659, 784, 1047]; // C5, E5, G5, C6
            let time = this.audioContext.currentTime;
            
            notes.forEach((freq, index) => {
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(this.masterGain);
                
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(freq, time);
                
                gainNode.gain.setValueAtTime(this.soundVolume * 0.4, time);
                gainNode.gain.exponentialRampToValueAtTime(0.01, time + 0.2);
                
                oscillator.start(time);
                oscillator.stop(time + 0.2);
                
                time += 0.15;
            });
        });
    }
    
    createBackgroundMusic() {
        // 创建简单的背景音乐循环
        this.music.set('background', () => {
            if (!this.audioContext) return null;
            
            const musicGain = this.audioContext.createGain();
            musicGain.connect(this.masterGain);
            musicGain.gain.setValueAtTime(this.musicVolume * 0.3, this.audioContext.currentTime);
            
            // 这里可以加载实际的音乐文件或创建更复杂的程序化音乐
            return musicGain;
        });
    }
    
    playSound(soundName) {
        if (this.muted || !this.sounds.has(soundName)) return;
        
        try {
            // 恢复音频上下文（如果被暂停）
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            
            const soundFunction = this.sounds.get(soundName);
            soundFunction();
        } catch (e) {
            console.warn(`Failed to play sound: ${soundName}`, e);
        }
    }
    
    playMusic(musicName) {
        if (this.muted || !this.music.has(musicName)) return;
        
        try {
            this.stopMusic();
            
            if (this.audioContext && this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }
            
            const musicFunction = this.music.get(musicName);
            this.currentMusic = musicFunction();
        } catch (e) {
            console.warn(`Failed to play music: ${musicName}`, e);
        }
    }
    
    stopMusic() {
        if (this.currentMusic) {
            try {
                this.currentMusic.disconnect();
                this.currentMusic = null;
            } catch (e) {
                console.warn('Failed to stop music', e);
            }
        }
    }
    
    setSoundVolume(volume) {
        this.soundVolume = MathUtils.clamp(volume, 0, 1);
        this.saveSettings();
    }
    
    setMusicVolume(volume) {
        this.musicVolume = MathUtils.clamp(volume, 0, 1);
        if (this.currentMusic) {
            this.currentMusic.gain.setValueAtTime(
                this.musicVolume * 0.3,
                this.audioContext.currentTime
            );
        }
        this.saveSettings();
    }
    
    setMuted(muted) {
        this.muted = muted;
        if (muted) {
            this.stopMusic();
        }
        this.saveSettings();
    }
    
    toggleMute() {
        this.setMuted(!this.muted);
        return this.muted;
    }
    
    saveSettings() {
        StorageUtils.save('audioSettings', {
            soundVolume: this.soundVolume,
            musicVolume: this.musicVolume,
            muted: this.muted
        });
    }
    
    loadSettings() {
        const settings = StorageUtils.load('audioSettings', {
            soundVolume: 0.7,
            musicVolume: 0.5,
            muted: false
        });
        
        this.soundVolume = settings.soundVolume;
        this.musicVolume = settings.musicVolume;
        this.muted = settings.muted;
    }
    
    // 创建音频可视化效果
    createVisualizer(canvas) {
        if (!this.audioContext) return null;
        
        const analyser = this.audioContext.createAnalyser();
        analyser.fftSize = 256;
        
        this.masterGain.connect(analyser);
        
        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);
        
        return {
            analyser,
            dataArray,
            bufferLength,
            draw: (ctx) => {
                analyser.getByteFrequencyData(dataArray);
                
                const barWidth = canvas.width / bufferLength;
                let x = 0;
                
                for (let i = 0; i < bufferLength; i++) {
                    const barHeight = (dataArray[i] / 255) * canvas.height * 0.5;
                    
                    const hue = (i / bufferLength) * 360;
                    ctx.fillStyle = `hsl(${hue}, 100%, 50%)`;
                    ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);
                    
                    x += barWidth;
                }
            }
        };
    }
    
    // 获取音频状态
    getStatus() {
        return {
            soundVolume: this.soundVolume,
            musicVolume: this.musicVolume,
            muted: this.muted,
            contextState: this.audioContext ? this.audioContext.state : 'unavailable',
            currentMusic: this.currentMusic ? 'playing' : 'stopped'
        };
    }
}

// 全局音频管理器实例
window.audioManager = new AudioManager();
