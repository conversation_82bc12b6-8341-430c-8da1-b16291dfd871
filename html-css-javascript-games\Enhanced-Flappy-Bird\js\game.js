// 游戏主逻辑

class Game {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = canvas.width;
        this.height = canvas.height;
        
        // 游戏状态
        this.state = 'menu'; // menu, playing, paused, gameOver
        this.lastTime = 0;
        this.gameTime = 0;
        this.timeScale = 1; // 用于慢动作效果
        
        // 游戏对象
        this.player = null;
        this.pipes = [];
        this.powerUps = [];
        this.coins = [];
        this.backgrounds = [];
        
        // 游戏设置
        this.difficulty = 'normal';
        this.pipeSpeed = 200;
        this.pipeGap = 150;
        this.pipeSpacing = 300;
        this.nextPipeX = this.width + 200;
        
        // 分数和统计
        this.score = 0;
        this.coins = 0;
        this.level = 1;
        this.experience = 0;
        this.experienceToNext = 100;
        this.lives = 1;
        this.consecutiveJumps = 0;
        this.powerUpsUsed = 0;
        this.perfectRun = true;
        
        // 特效状态
        this.slowMotionTime = 0;
        this.doubleScoreTime = 0;
        this.magnetismTime = 0;
        this.speedBoostTime = 0;
        
        // 系统初始化
        this.particleSystem = new ParticleSystem(canvas);
        this.backgroundParticles = new BackgroundParticles(canvas);
        
        // 事件监听
        this.setupEventListeners();
        
        // 加载游戏数据
        this.loadGameData();
        
        window.particleSystem = this.particleSystem;
        window.gameCanvas = canvas;
    }
    
    setupEventListeners() {
        // 游戏事件
        window.gameEvents.on('playerJump', () => {
            this.consecutiveJumps++;
        });
        
        window.gameEvents.on('playerDied', () => {
            this.onPlayerDied();
        });
        
        window.gameEvents.on('powerUpCollected', (data) => {
            this.onPowerUpCollected(data);
        });
        
        window.gameEvents.on('coinCollected', (data) => {
            this.onCoinCollected(data);
        });
        
        window.gameEvents.on('slowMotionActivated', (duration) => {
            this.slowMotionTime = duration;
            this.timeScale = 0.5;
        });
        
        window.gameEvents.on('doubleScoreActivated', (duration) => {
            this.doubleScoreTime = duration;
        });
        
        window.gameEvents.on('magnetismActivated', (duration) => {
            this.magnetismTime = duration;
            this.activateMagnetism();
        });
        
        window.gameEvents.on('speedBoostActivated', (duration) => {
            this.speedBoostTime = duration;
        });
        
        window.gameEvents.on('achievementUnlocked', (achievement) => {
            this.onAchievementUnlocked(achievement);
        });
    }
    
    startGame(characterType = 'classic') {
        this.state = 'playing';
        this.gameTime = 0;
        this.score = 0;
        this.consecutiveJumps = 0;
        this.powerUpsUsed = 0;
        this.perfectRun = true;
        this.lives = 1;
        
        // 重置特效
        this.slowMotionTime = 0;
        this.doubleScoreTime = 0;
        this.magnetismTime = 0;
        this.speedBoostTime = 0;
        this.timeScale = 1;
        
        // 创建玩家
        this.player = new Character(characterType, 100, this.height / 2);
        
        // 清空游戏对象
        this.pipes = [];
        this.powerUps = [];
        this.coins = [];
        
        // 重置管道生成
        this.nextPipeX = this.width + 200;
        
        // 清空粒子
        this.particleSystem.clear();
        
        // 播放背景音乐
        window.audioManager?.playMusic('background');
        
        // 更新统计
        this.updateGameStats();
    }
    
    pauseGame() {
        if (this.state === 'playing') {
            this.state = 'paused';
        }
    }
    
    resumeGame() {
        if (this.state === 'paused') {
            this.state = 'playing';
        }
    }
    
    endGame() {
        this.state = 'gameOver';
        
        // 停止音乐
        window.audioManager?.stopMusic();
        window.audioManager?.playSound('gameover');
        
        // 更新统计
        this.updateFinalStats();
        
        // 检查成就
        this.checkAchievements();
        
        // 保存数据
        this.saveGameData();
    }
    
    update(currentTime) {
        const deltaTime = (currentTime - this.lastTime) * this.timeScale;
        this.lastTime = currentTime;
        
        if (this.state !== 'playing') return;
        
        this.gameTime += deltaTime;
        
        // 更新特效时间
        this.updateEffectTimers(deltaTime);
        
        // 更新玩家
        if (this.player) {
            this.player.update(deltaTime);
            
            // 检查边界
            if (this.player.y < 0 || this.player.y + this.player.height > this.height) {
                this.player.takeDamage();
            }
        }
        
        // 更新管道
        this.updatePipes(deltaTime);
        
        // 更新道具
        this.updatePowerUps(deltaTime);
        
        // 更新金币
        this.updateCoins(deltaTime);
        
        // 生成新对象
        this.spawnObjects();
        
        // 检查碰撞
        this.checkCollisions();
        
        // 更新粒子系统
        this.particleSystem.update(currentTime);
        this.backgroundParticles.update();
        
        // 更新UI
        this.updateUI();
    }
    
    updateEffectTimers(deltaTime) {
        // 慢动作效果
        if (this.slowMotionTime > 0) {
            this.slowMotionTime -= deltaTime;
            if (this.slowMotionTime <= 0) {
                this.timeScale = 1;
            }
        }
        
        // 双倍得分效果
        if (this.doubleScoreTime > 0) {
            this.doubleScoreTime -= deltaTime;
        }
        
        // 磁铁效果
        if (this.magnetismTime > 0) {
            this.magnetismTime -= deltaTime;
            if (this.magnetismTime <= 0) {
                this.deactivateMagnetism();
            }
        }
        
        // 速度提升效果
        if (this.speedBoostTime > 0) {
            this.speedBoostTime -= deltaTime;
        }
    }
    
    updatePipes(deltaTime) {
        const speed = this.pipeSpeed * (this.speedBoostTime > 0 ? 1.5 : 1);
        
        this.pipes.forEach(pipe => {
            pipe.x -= speed * deltaTime * 0.001;
        });
        
        // 移除屏幕外的管道
        this.pipes = this.pipes.filter(pipe => pipe.x + pipe.width > -100);
    }
    
    updatePowerUps(deltaTime) {
        const speed = this.pipeSpeed * (this.speedBoostTime > 0 ? 1.5 : 1);
        
        this.powerUps.forEach(powerUp => {
            powerUp.update(deltaTime);
            powerUp.x -= speed * deltaTime * 0.001;
        });
        
        // 移除屏幕外的道具
        this.powerUps = this.powerUps.filter(powerUp => 
            !powerUp.collected && powerUp.x + powerUp.width > -50
        );
    }
    
    updateCoins(deltaTime) {
        const speed = this.pipeSpeed * (this.speedBoostTime > 0 ? 1.5 : 1);
        
        this.coins.forEach(coin => {
            coin.update(deltaTime);
            coin.x -= speed * deltaTime * 0.001;
        });
        
        // 移除屏幕外的金币
        this.coins = this.coins.filter(coin => 
            !coin.collected && coin.x + coin.width > -50
        );
    }
    
    spawnObjects() {
        // 生成管道
        if (this.nextPipeX <= this.width) {
            this.spawnPipe();
            this.nextPipeX += this.pipeSpacing;
        }
    }
    
    spawnPipe() {
        const gapY = MathUtils.random(100, this.height - this.pipeGap - 100);
        
        // 上管道
        this.pipes.push({
            x: this.width,
            y: 0,
            width: 60,
            height: gapY,
            passed: false,
            type: 'top'
        });
        
        // 下管道
        this.pipes.push({
            x: this.width,
            y: gapY + this.pipeGap,
            width: 60,
            height: this.height - (gapY + this.pipeGap),
            passed: false,
            type: 'bottom'
        });
        
        // 生成道具
        if (PowerUpManager.shouldSpawnPowerUp()) {
            const powerUpType = PowerUpManager.getRandomPowerUp();
            this.powerUps.push(new PowerUp(
                this.width + 30,
                gapY + this.pipeGap / 2 - 15,
                powerUpType
            ));
        }
        
        // 生成金币
        if (PowerUpManager.shouldSpawnCoin()) {
            const coinValue = PowerUpManager.getRandomCoinValue();
            const coinCount = MathUtils.randomInt(1, 3);
            
            for (let i = 0; i < coinCount; i++) {
                this.coins.push(new Coin(
                    this.width + 30 + i * 25,
                    gapY + MathUtils.random(20, this.pipeGap - 40),
                    coinValue
                ));
            }
        }
    }
    
    checkCollisions() {
        if (!this.player || !this.player.isAlive) return;
        
        const playerBounds = this.player.getBounds();
        
        // 检查管道碰撞
        this.pipes.forEach(pipe => {
            if (CollisionUtils.rectRect(playerBounds, pipe)) {
                if (!this.player.isInvulnerable) {
                    this.player.takeDamage();
                    this.consecutiveJumps = 0;
                }
            }
            
            // 检查得分
            if (!pipe.passed && pipe.x + pipe.width < this.player.x) {
                pipe.passed = true;
                if (pipe.type === 'top') { // 只在上管道计分
                    this.addScore(1);
                }
            }
        });
        
        // 检查道具碰撞
        this.powerUps.forEach(powerUp => {
            if (!powerUp.collected && 
                CollisionUtils.rectRect(playerBounds, powerUp.getBounds())) {
                powerUp.collect(this.player);
                this.perfectRun = false;
            }
        });
        
        // 检查金币碰撞
        this.coins.forEach(coin => {
            if (!coin.collected && 
                CollisionUtils.rectRect(playerBounds, coin.getBounds())) {
                coin.collect();
                this.addCoins(coin.value);
            }
        });
    }
    
    addScore(points) {
        const multiplier = this.doubleScoreTime > 0 ? 2 : 1;
        this.score += points * multiplier;
        
        // 播放得分音效
        window.audioManager?.playSound('score');
        
        // 得分特效
        this.particleSystem.createCollectEffect(
            this.player.x + this.player.width / 2,
            this.player.y + this.player.height / 2
        );
        
        // 检查升级
        this.checkLevelUp();
    }
    
    addCoins(amount) {
        this.coins += amount;
        window.audioManager?.playSound('coin');
    }
    
    addExperience(amount) {
        this.experience += amount;
        this.checkLevelUp();
    }
    
    checkLevelUp() {
        if (this.experience >= this.experienceToNext) {
            this.level++;
            this.experience -= this.experienceToNext;
            this.experienceToNext = Math.floor(this.experienceToNext * 1.2);
            
            // 升级特效
            this.particleSystem.createExplosion(
                this.player.x + this.player.width / 2,
                this.player.y + this.player.height / 2,
                '#FFD700'
            );
            
            window.audioManager?.playSound('achievement');
        }
    }
    
    activateMagnetism() {
        this.coins.forEach(coin => {
            coin.setMagnetTarget({
                x: this.player.x + this.player.width / 2,
                y: this.player.y + this.player.height / 2
            });
        });
    }
    
    deactivateMagnetism() {
        this.coins.forEach(coin => {
            coin.removeMagnetEffect();
        });
    }
    
    onPlayerDied() {
        this.lives--;
        if (this.lives <= 0) {
            this.endGame();
        } else {
            // 复活逻辑
            this.player.reset(100, this.height / 2);
        }
    }
    
    onPowerUpCollected(data) {
        this.powerUpsUsed++;
        this.addScore(data.points);
        this.addExperience(data.points / 2);
    }
    
    onCoinCollected(data) {
        this.addCoins(data.value);
        this.addExperience(data.value);
    }
    
    onAchievementUnlocked(achievement) {
        window.audioManager?.playSound('achievement');
        
        // 成就解锁特效
        this.particleSystem.createExplosion(
            this.width / 2,
            this.height / 2,
            '#FFD700'
        );
    }
    
    updateGameStats() {
        const stats = this.getGameStats();
        window.achievementManager?.updateProgress(stats);
    }
    
    updateFinalStats() {
        // 更新最高分
        const savedData = this.loadGameData();
        if (this.score > savedData.highScore) {
            savedData.highScore = this.score;
        }
        
        // 更新总统计
        savedData.totalScore += this.score;
        savedData.totalCoins += this.coins;
        savedData.gamesPlayed++;
        savedData.powerUpsUsed += this.powerUpsUsed;
        
        if (this.perfectRun && this.score >= 20) {
            savedData.perfectRuns++;
        }
        
        this.saveGameData(savedData);
    }
    
    checkAchievements() {
        const stats = this.getGameStats();
        window.achievementManager?.updateProgress(stats);
    }
    
    getGameStats() {
        const savedData = this.loadGameData();
        return {
            currentScore: this.score,
            highScore: Math.max(this.score, savedData.highScore),
            totalScore: savedData.totalScore + this.score,
            totalCoins: savedData.totalCoins + this.coins,
            gamesPlayed: savedData.gamesPlayed + 1,
            powerUpsUsed: savedData.powerUpsUsed + this.powerUpsUsed,
            currentTimeAlive: this.gameTime,
            consecutiveJumps: this.consecutiveJumps,
            perfectRuns: savedData.perfectRuns + (this.perfectRun && this.score >= 20 ? 1 : 0),
            charactersUnlocked: savedData.charactersUnlocked || 1,
            level: this.level
        };
    }
    
    updateUI() {
        // 更新分数显示
        const scoreElement = DOMUtils.get('#currentScore');
        if (scoreElement) scoreElement.textContent = this.score;
        
        // 更新等级显示
        const levelElement = DOMUtils.get('#currentLevel');
        if (levelElement) levelElement.textContent = this.level;
        
        // 更新经验条
        const expFill = DOMUtils.get('#expFill');
        if (expFill) {
            const percentage = (this.experience / this.experienceToNext) * 100;
            expFill.style.width = `${percentage}%`;
        }
        
        // 更新道具计时器
        this.updatePowerUpTimers();
    }
    
    updatePowerUpTimers() {
        // 护盾计时器
        const shieldPowerUp = DOMUtils.get('#shieldPowerUp');
        const shieldTimer = DOMUtils.get('#shieldTimer');
        if (this.player && this.player.isInvulnerable) {
            DOMUtils.show(shieldPowerUp);
            if (shieldTimer) {
                shieldTimer.textContent = Math.ceil(this.player.invulnerabilityTime / 1000);
            }
        } else {
            DOMUtils.hide(shieldPowerUp);
        }
        
        // 慢动作计时器
        const slowMotionPowerUp = DOMUtils.get('#slowMotionPowerUp');
        const slowMotionTimer = DOMUtils.get('#slowMotionTimer');
        if (this.slowMotionTime > 0) {
            DOMUtils.show(slowMotionPowerUp);
            if (slowMotionTimer) {
                slowMotionTimer.textContent = Math.ceil(this.slowMotionTime / 1000);
            }
        } else {
            DOMUtils.hide(slowMotionPowerUp);
        }
    }
    
    saveGameData(data = null) {
        const gameData = data || {
            highScore: this.score,
            totalScore: this.score,
            totalCoins: this.coins,
            gamesPlayed: 1,
            powerUpsUsed: this.powerUpsUsed,
            perfectRuns: this.perfectRun && this.score >= 20 ? 1 : 0,
            charactersUnlocked: 1,
            level: this.level,
            experience: this.experience
        };
        
        StorageUtils.save('gameData', gameData);
    }
    
    loadGameData() {
        return StorageUtils.load('gameData', {
            highScore: 0,
            totalScore: 0,
            totalCoins: 0,
            gamesPlayed: 0,
            powerUpsUsed: 0,
            perfectRuns: 0,
            charactersUnlocked: 1,
            level: 1,
            experience: 0
        });
    }
}
