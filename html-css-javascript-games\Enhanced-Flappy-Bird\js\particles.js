// 粒子系统

class Particle {
    constructor(x, y, options = {}) {
        this.x = x;
        this.y = y;
        this.vx = options.vx || MathUtils.random(-2, 2);
        this.vy = options.vy || MathUtils.random(-2, 2);
        this.life = options.life || 1.0;
        this.maxLife = this.life;
        this.size = options.size || MathUtils.random(2, 6);
        this.color = options.color || ColorUtils.randomColor();
        this.gravity = options.gravity || 0;
        this.friction = options.friction || 0.98;
        this.alpha = options.alpha || 1.0;
        this.rotation = options.rotation || 0;
        this.rotationSpeed = options.rotationSpeed || MathUtils.random(-0.1, 0.1);
        this.shape = options.shape || 'circle'; // circle, square, star, heart
        this.trail = options.trail || false;
        this.trailPoints = [];
    }
    
    update(deltaTime) {
        // 更新位置
        this.x += this.vx * deltaTime;
        this.y += this.vy * deltaTime;
        
        // 应用重力
        this.vy += this.gravity * deltaTime;
        
        // 应用摩擦力
        this.vx *= this.friction;
        this.vy *= this.friction;
        
        // 更新旋转
        this.rotation += this.rotationSpeed * deltaTime;
        
        // 更新生命值
        this.life -= deltaTime / 1000;
        
        // 更新透明度
        this.alpha = this.life / this.maxLife;
        
        // 更新轨迹
        if (this.trail) {
            this.trailPoints.push({ x: this.x, y: this.y, alpha: this.alpha });
            if (this.trailPoints.length > 10) {
                this.trailPoints.shift();
            }
        }
        
        return this.life > 0;
    }
    
    draw(ctx) {
        ctx.save();
        ctx.globalAlpha = this.alpha;
        ctx.translate(this.x, this.y);
        ctx.rotate(this.rotation);
        
        // 绘制轨迹
        if (this.trail && this.trailPoints.length > 1) {
            ctx.strokeStyle = this.color;
            ctx.lineWidth = this.size / 2;
            ctx.beginPath();
            for (let i = 0; i < this.trailPoints.length; i++) {
                const point = this.trailPoints[i];
                ctx.globalAlpha = point.alpha * 0.5;
                if (i === 0) {
                    ctx.moveTo(point.x - this.x, point.y - this.y);
                } else {
                    ctx.lineTo(point.x - this.x, point.y - this.y);
                }
            }
            ctx.stroke();
        }
        
        ctx.globalAlpha = this.alpha;
        ctx.fillStyle = this.color;
        
        // 根据形状绘制
        switch (this.shape) {
            case 'circle':
                ctx.beginPath();
                ctx.arc(0, 0, this.size, 0, Math.PI * 2);
                ctx.fill();
                break;
                
            case 'square':
                ctx.fillRect(-this.size/2, -this.size/2, this.size, this.size);
                break;
                
            case 'star':
                this.drawStar(ctx, 0, 0, 5, this.size, this.size/2);
                break;
                
            case 'heart':
                this.drawHeart(ctx, 0, 0, this.size);
                break;
        }
        
        ctx.restore();
    }
    
    drawStar(ctx, x, y, spikes, outerRadius, innerRadius) {
        let rot = Math.PI / 2 * 3;
        let step = Math.PI / spikes;
        
        ctx.beginPath();
        ctx.moveTo(x, y - outerRadius);
        
        for (let i = 0; i < spikes; i++) {
            ctx.lineTo(x + Math.cos(rot) * outerRadius, y + Math.sin(rot) * outerRadius);
            rot += step;
            ctx.lineTo(x + Math.cos(rot) * innerRadius, y + Math.sin(rot) * innerRadius);
            rot += step;
        }
        
        ctx.lineTo(x, y - outerRadius);
        ctx.closePath();
        ctx.fill();
    }
    
    drawHeart(ctx, x, y, size) {
        ctx.beginPath();
        ctx.moveTo(x, y + size/4);
        ctx.bezierCurveTo(x, y, x - size/2, y, x - size/2, y + size/4);
        ctx.bezierCurveTo(x - size/2, y + size/2, x, y + size/2, x, y + size);
        ctx.bezierCurveTo(x, y + size/2, x + size/2, y + size/2, x + size/2, y + size/4);
        ctx.bezierCurveTo(x + size/2, y, x, y, x, y + size/4);
        ctx.fill();
    }
}

class ParticleSystem {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.particles = [];
        this.lastTime = 0;
    }
    
    // 添加粒子
    addParticle(x, y, options = {}) {
        this.particles.push(new Particle(x, y, options));
    }
    
    // 批量添加粒子
    addParticles(x, y, count, options = {}) {
        for (let i = 0; i < count; i++) {
            const particleOptions = { ...options };
            
            // 添加一些随机性
            if (options.spread) {
                particleOptions.vx = MathUtils.random(-options.spread, options.spread);
                particleOptions.vy = MathUtils.random(-options.spread, options.spread);
            }
            
            this.addParticle(
                x + MathUtils.random(-10, 10),
                y + MathUtils.random(-10, 10),
                particleOptions
            );
        }
    }
    
    // 创建爆炸效果
    createExplosion(x, y, color = null) {
        const colors = color ? [color] : ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];
        
        for (let i = 0; i < 20; i++) {
            const angle = (Math.PI * 2 * i) / 20;
            const speed = MathUtils.random(50, 150);
            
            this.addParticle(x, y, {
                vx: Math.cos(angle) * speed,
                vy: Math.sin(angle) * speed,
                life: MathUtils.random(500, 1000),
                size: MathUtils.random(3, 8),
                color: colors[MathUtils.randomInt(0, colors.length - 1)],
                gravity: 50,
                friction: 0.95,
                shape: 'circle'
            });
        }
    }
    
    // 创建彩虹轨迹
    createRainbowTrail(x, y) {
        this.addParticle(x, y, {
            vx: MathUtils.random(-20, 20),
            vy: MathUtils.random(-20, 20),
            life: 1000,
            size: MathUtils.random(4, 8),
            color: ColorUtils.rainbowColor(Date.now()),
            gravity: 0,
            friction: 0.98,
            shape: 'star',
            trail: true
        });
    }
    
    // 创建收集效果
    createCollectEffect(x, y) {
        const colors = ['#FFD700', '#FFA500', '#FF6347'];
        
        for (let i = 0; i < 10; i++) {
            this.addParticle(x, y, {
                vx: MathUtils.random(-30, 30),
                vy: MathUtils.random(-50, -10),
                life: MathUtils.random(800, 1200),
                size: MathUtils.random(3, 6),
                color: colors[MathUtils.randomInt(0, colors.length - 1)],
                gravity: 30,
                friction: 0.97,
                shape: 'circle'
            });
        }
    }
    
    // 创建治愈效果
    createHealEffect(x, y) {
        for (let i = 0; i < 15; i++) {
            this.addParticle(x, y, {
                vx: MathUtils.random(-20, 20),
                vy: MathUtils.random(-40, -20),
                life: MathUtils.random(1000, 1500),
                size: MathUtils.random(4, 7),
                color: '#4ECDC4',
                gravity: -20, // 向上飘
                friction: 0.98,
                shape: 'heart'
            });
        }
    }
    
    // 创建护盾效果
    createShieldEffect(x, y) {
        for (let i = 0; i < 8; i++) {
            const angle = (Math.PI * 2 * i) / 8;
            const radius = 30;
            
            this.addParticle(
                x + Math.cos(angle) * radius,
                y + Math.sin(angle) * radius,
                {
                    vx: Math.cos(angle) * 20,
                    vy: Math.sin(angle) * 20,
                    life: 800,
                    size: 6,
                    color: '#45B7D1',
                    gravity: 0,
                    friction: 0.95,
                    shape: 'square'
                }
            );
        }
    }
    
    // 创建速度线效果
    createSpeedLines(x, y, direction = 1) {
        for (let i = 0; i < 5; i++) {
            this.addParticle(x - direction * 50, y + MathUtils.random(-20, 20), {
                vx: direction * MathUtils.random(100, 200),
                vy: MathUtils.random(-10, 10),
                life: 300,
                size: MathUtils.random(2, 4),
                color: '#FFFFFF',
                gravity: 0,
                friction: 0.99,
                shape: 'square'
            });
        }
    }
    
    // 更新所有粒子
    update(currentTime) {
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        // 更新粒子并移除死亡的粒子
        this.particles = this.particles.filter(particle => particle.update(deltaTime));
    }
    
    // 绘制所有粒子
    draw() {
        this.particles.forEach(particle => particle.draw(this.ctx));
    }
    
    // 清除所有粒子
    clear() {
        this.particles = [];
    }
    
    // 获取粒子数量
    getParticleCount() {
        return this.particles.length;
    }
}

// 背景粒子效果
class BackgroundParticles {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.particles = [];
        this.init();
    }
    
    init() {
        // 创建背景粒子
        for (let i = 0; i < 50; i++) {
            this.particles.push({
                x: MathUtils.random(0, this.canvas.width),
                y: MathUtils.random(0, this.canvas.height),
                vx: MathUtils.random(-0.5, 0.5),
                vy: MathUtils.random(-0.5, 0.5),
                size: MathUtils.random(1, 3),
                alpha: MathUtils.random(0.1, 0.3),
                color: '#FFFFFF'
            });
        }
    }
    
    update() {
        this.particles.forEach(particle => {
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // 边界检查
            if (particle.x < 0) particle.x = this.canvas.width;
            if (particle.x > this.canvas.width) particle.x = 0;
            if (particle.y < 0) particle.y = this.canvas.height;
            if (particle.y > this.canvas.height) particle.y = 0;
        });
    }
    
    draw() {
        this.ctx.save();
        this.particles.forEach(particle => {
            this.ctx.globalAlpha = particle.alpha;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
        });
        this.ctx.restore();
    }
}
