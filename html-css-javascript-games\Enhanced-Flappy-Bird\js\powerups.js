// 道具系统

class PowerUp {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.width = 30;
        this.height = 30;
        this.collected = false;
        this.animationTime = 0;
        this.bobOffset = 0;
        this.rotationSpeed = 0.05;
        this.rotation = 0;
        this.scale = 1;
        this.glowIntensity = 0;
        
        // 从配置获取道具属性
        const config = PowerUpManager.getPowerUpConfig(type);
        this.color = config.color;
        this.emoji = config.emoji;
        this.effect = config.effect;
        this.duration = config.duration;
        this.rarity = config.rarity;
        this.points = config.points;
        
        // 根据稀有度调整外观
        this.setupRarityEffects();
    }
    
    setupRarityEffects() {
        switch (this.rarity) {
            case 'common':
                this.glowColor = '#FFFFFF';
                this.rotationSpeed = 0.02;
                break;
            case 'rare':
                this.glowColor = '#4ECDC4';
                this.rotationSpeed = 0.04;
                this.scale = 1.2;
                break;
            case 'epic':
                this.glowColor = '#9B59B6';
                this.rotationSpeed = 0.06;
                this.scale = 1.4;
                break;
            case 'legendary':
                this.glowColor = '#FFD700';
                this.rotationSpeed = 0.08;
                this.scale = 1.6;
                break;
        }
    }
    
    update(deltaTime) {
        if (this.collected) return;
        
        // 更新动画
        this.animationTime += deltaTime;
        this.bobOffset = Math.sin(this.animationTime * 0.003) * 5;
        this.rotation += this.rotationSpeed * deltaTime;
        this.glowIntensity = (Math.sin(this.animationTime * 0.005) + 1) * 0.5;
        
        // 移动（如果有速度）
        if (this.velocity) {
            this.x += this.velocity.x * deltaTime * 0.001;
            this.y += this.velocity.y * deltaTime * 0.001;
        }
        
        // 稀有道具的特殊效果
        if (this.rarity === 'legendary' && Math.random() < 0.1) {
            window.particleSystem?.addParticle(
                this.x + this.width / 2,
                this.y + this.height / 2,
                {
                    vx: MathUtils.random(-20, 20),
                    vy: MathUtils.random(-20, 20),
                    life: 500,
                    size: 3,
                    color: this.glowColor,
                    gravity: 0,
                    shape: 'star'
                }
            );
        }
    }
    
    draw(ctx) {
        if (this.collected) return;
        
        ctx.save();
        
        // 绘制光晕
        if (this.rarity !== 'common') {
            const glowRadius = 20 + this.glowIntensity * 10;
            const gradient = ctx.createRadialGradient(
                this.x + this.width / 2, this.y + this.height / 2 + this.bobOffset, 0,
                this.x + this.width / 2, this.y + this.height / 2 + this.bobOffset, glowRadius
            );
            gradient.addColorStop(0, this.glowColor + '40');
            gradient.addColorStop(1, this.glowColor + '00');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(
                this.x + this.width / 2,
                this.y + this.height / 2 + this.bobOffset,
                glowRadius,
                0,
                Math.PI * 2
            );
            ctx.fill();
        }
        
        // 绘制道具主体
        ctx.translate(
            this.x + this.width / 2,
            this.y + this.height / 2 + this.bobOffset
        );
        ctx.rotate(this.rotation);
        ctx.scale(this.scale, this.scale);
        
        // 背景圆圈
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 边框
        ctx.strokeStyle = this.glowColor;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 绘制图标
        ctx.font = `${this.width * 0.6}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(this.emoji, 0, 0);
        
        ctx.restore();
    }
    
    collect(player) {
        if (this.collected) return false;
        
        this.collected = true;
        
        // 应用效果
        this.applyEffect(player);
        
        // 收集特效
        window.particleSystem?.createCollectEffect(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        
        // 播放音效
        window.audioManager?.playSound('powerup');
        
        // 发射事件
        window.gameEvents?.emit('powerUpCollected', {
            type: this.type,
            points: this.points,
            player: player
        });
        
        return true;
    }
    
    applyEffect(player) {
        switch (this.type) {
            case 'shield':
                player.activateShield();
                break;
                
            case 'slowMotion':
                window.gameEvents?.emit('slowMotionActivated', this.duration);
                break;
                
            case 'speedBoost':
                window.gameEvents?.emit('speedBoostActivated', this.duration);
                break;
                
            case 'magnetCoin':
                window.gameEvents?.emit('magnetismActivated', this.duration);
                break;
                
            case 'extraLife':
                window.gameEvents?.emit('extraLifeGained');
                break;
                
            case 'doubleScore':
                window.gameEvents?.emit('doubleScoreActivated', this.duration);
                break;
                
            case 'invincibility':
                player.isInvulnerable = true;
                player.invulnerabilityTime = this.duration;
                break;
                
            case 'teleport':
                player.teleport();
                break;
        }
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

class Coin {
    constructor(x, y, value = 1) {
        this.x = x;
        this.y = y;
        this.value = value;
        this.width = 20;
        this.height = 20;
        this.collected = false;
        this.animationTime = 0;
        this.rotation = 0;
        this.bobOffset = 0;
        this.magnetized = false;
        this.magnetTarget = null;
        
        // 根据价值设置外观
        this.setupAppearance();
    }
    
    setupAppearance() {
        switch (this.value) {
            case 1:
                this.color = '#FFD700';
                this.emoji = '🪙';
                break;
            case 5:
                this.color = '#C0C0C0';
                this.emoji = '💰';
                this.width = 25;
                this.height = 25;
                break;
            case 10:
                this.color = '#CD7F32';
                this.emoji = '💎';
                this.width = 30;
                this.height = 30;
                break;
        }
    }
    
    update(deltaTime) {
        if (this.collected) return;
        
        this.animationTime += deltaTime;
        this.rotation += 0.05 * deltaTime;
        this.bobOffset = Math.sin(this.animationTime * 0.003) * 3;
        
        // 磁铁效果
        if (this.magnetized && this.magnetTarget) {
            const dx = this.magnetTarget.x - this.x;
            const dy = this.magnetTarget.y - this.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance > 5) {
                const speed = 200;
                this.x += (dx / distance) * speed * deltaTime * 0.001;
                this.y += (dy / distance) * speed * deltaTime * 0.001;
            }
        }
        
        // 移动（如果有速度）
        if (this.velocity) {
            this.x += this.velocity.x * deltaTime * 0.001;
            this.y += this.velocity.y * deltaTime * 0.001;
        }
    }
    
    draw(ctx) {
        if (this.collected) return;
        
        ctx.save();
        
        // 磁铁效果光晕
        if (this.magnetized) {
            const gradient = ctx.createRadialGradient(
                this.x + this.width / 2, this.y + this.height / 2 + this.bobOffset, 0,
                this.x + this.width / 2, this.y + this.height / 2 + this.bobOffset, 25
            );
            gradient.addColorStop(0, '#4ECDC4' + '60');
            gradient.addColorStop(1, '#4ECDC4' + '00');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(
                this.x + this.width / 2,
                this.y + this.height / 2 + this.bobOffset,
                25,
                0,
                Math.PI * 2
            );
            ctx.fill();
        }
        
        ctx.translate(
            this.x + this.width / 2,
            this.y + this.height / 2 + this.bobOffset
        );
        ctx.rotate(this.rotation);
        
        // 绘制金币
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.width / 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 边框
        ctx.strokeStyle = '#FFA500';
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 图标
        ctx.font = `${this.width * 0.6}px Arial`;
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillStyle = '#FFFFFF';
        ctx.fillText(this.emoji, 0, 0);
        
        ctx.restore();
    }
    
    collect() {
        if (this.collected) return false;
        
        this.collected = true;
        
        // 收集特效
        window.particleSystem?.createCollectEffect(
            this.x + this.width / 2,
            this.y + this.height / 2
        );
        
        // 播放音效
        window.audioManager?.playSound('coin');
        
        // 发射事件
        window.gameEvents?.emit('coinCollected', {
            value: this.value,
            x: this.x,
            y: this.y
        });
        
        return true;
    }
    
    setMagnetTarget(target) {
        this.magnetized = true;
        this.magnetTarget = target;
    }
    
    removeMagnetEffect() {
        this.magnetized = false;
        this.magnetTarget = null;
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// 道具管理器
class PowerUpManager {
    static powerUps = {
        shield: {
            emoji: '🛡️',
            color: '#45B7D1',
            effect: 'shield',
            duration: 3000,
            rarity: 'common',
            points: 50,
            spawnChance: 0.15,
            description: '提供3秒无敌保护'
        },
        
        slowMotion: {
            emoji: '⏰',
            color: '#9B59B6',
            effect: 'slowMotion',
            duration: 3000,
            rarity: 'rare',
            points: 75,
            spawnChance: 0.08,
            description: '减慢游戏速度3秒'
        },
        
        speedBoost: {
            emoji: '⚡',
            color: '#F39C12',
            effect: 'speedBoost',
            duration: 5000,
            rarity: 'common',
            points: 40,
            spawnChance: 0.12,
            description: '增加移动速度5秒'
        },
        
        magnetCoin: {
            emoji: '🧲',
            color: '#E74C3C',
            effect: 'magnetCoin',
            duration: 5000,
            rarity: 'rare',
            points: 100,
            spawnChance: 0.06,
            description: '吸引附近的金币5秒'
        },
        
        extraLife: {
            emoji: '❤️',
            color: '#E91E63',
            effect: 'extraLife',
            duration: 0,
            rarity: 'epic',
            points: 200,
            spawnChance: 0.03,
            description: '获得一条额外生命'
        },
        
        doubleScore: {
            emoji: '✨',
            color: '#FFD700',
            effect: 'doubleScore',
            duration: 10000,
            rarity: 'epic',
            points: 150,
            spawnChance: 0.04,
            description: '双倍得分10秒'
        },
        
        invincibility: {
            emoji: '🌟',
            color: '#FF6B6B',
            effect: 'invincibility',
            duration: 5000,
            rarity: 'legendary',
            points: 300,
            spawnChance: 0.01,
            description: '完全无敌5秒'
        },
        
        teleport: {
            emoji: '🌀',
            color: '#6C5CE7',
            effect: 'teleport',
            duration: 0,
            rarity: 'rare',
            points: 80,
            spawnChance: 0.07,
            description: '立即传送到安全位置'
        }
    };
    
    static getPowerUpConfig(type) {
        return this.powerUps[type] || this.powerUps.shield;
    }
    
    static getRandomPowerUp() {
        const powerUpTypes = Object.keys(this.powerUps);
        const weights = powerUpTypes.map(type => this.powerUps[type].spawnChance);
        
        // 加权随机选择
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        let random = Math.random() * totalWeight;
        
        for (let i = 0; i < powerUpTypes.length; i++) {
            random -= weights[i];
            if (random <= 0) {
                return powerUpTypes[i];
            }
        }
        
        return powerUpTypes[0]; // 默认返回第一个
    }
    
    static shouldSpawnPowerUp() {
        return Math.random() < 0.3; // 30%概率生成道具
    }
    
    static shouldSpawnCoin() {
        return Math.random() < 0.6; // 60%概率生成金币
    }
    
    static getRandomCoinValue() {
        const random = Math.random();
        if (random < 0.7) return 1;      // 70% 普通金币
        if (random < 0.95) return 5;     // 25% 银币
        return 10;                       // 5% 钻石
    }
}
