// 主控制器 - 整合所有系统

class GameController {
    constructor() {
        this.canvas = null;
        this.game = null;
        this.renderer = null;
        this.inputHandler = null;
        this.uiManager = null;
        
        this.isRunning = false;
        this.animationId = null;
        
        // 当前选择的角色
        this.selectedCharacter = 'classic';
        this.selectedLevel = 'tutorial';
        
        // 初始化
        this.init();
    }
    
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    setup() {
        // 获取画布
        this.canvas = document.getElementById('gameCanvas');
        if (!this.canvas) {
            console.error('Game canvas not found!');
            return;
        }
        
        // 设置画布大小
        this.resizeCanvas();
        
        // 创建游戏实例
        this.game = new Game(this.canvas);
        
        // 创建渲染器
        this.renderer = new Renderer(this.canvas, this.game);
        
        // 创建输入处理器
        this.inputHandler = new InputHandler(this.game, this);
        
        // 创建UI管理器
        this.uiManager = new UIManager(this.game, this);
        
        // 设置事件监听
        this.setupEventListeners();
        
        // 开始游戏循环
        this.startGameLoop();
        
        console.log('Enhanced Flappy Bird initialized successfully!');
    }
    
    setupEventListeners() {
        // 窗口大小改变
        window.addEventListener('resize', () => this.resizeCanvas());
        
        // 游戏事件
        window.gameEvents.on('gameStarted', (data) => {
            this.onGameStarted(data);
        });
        
        window.gameEvents.on('gameEnded', (data) => {
            this.onGameEnded(data);
        });
        
        window.gameEvents.on('levelCompleted', (data) => {
            this.onLevelCompleted(data);
        });
        
        window.gameEvents.on('characterSelected', (character) => {
            this.selectedCharacter = character;
        });
        
        window.gameEvents.on('levelSelected', (level) => {
            this.selectedLevel = level;
        });
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.game.state === 'playing') {
                this.game.pauseGame();
            }
        });
    }
    
    resizeCanvas() {
        const container = this.canvas.parentElement;
        const rect = container.getBoundingClientRect();
        
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        
        if (this.renderer) {
            this.renderer.resize(this.canvas.width, this.canvas.height);
        }
    }
    
    startGameLoop() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.gameLoop();
    }
    
    stopGameLoop() {
        this.isRunning = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
    }
    
    gameLoop(currentTime = 0) {
        if (!this.isRunning) return;
        
        // 更新游戏
        this.game.update(currentTime);
        
        // 渲染游戏
        this.renderer.render(currentTime);
        
        // 更新UI
        this.uiManager.update();
        
        // 继续循环
        this.animationId = requestAnimationFrame((time) => this.gameLoop(time));
    }
    
    startGame() {
        // 应用选择的关卡
        const level = window.levelManager.getLevel(this.selectedLevel);
        if (level) {
            window.levelManager.setCurrentLevel(this.selectedLevel);
            level.applyToGame(this.game);
        }
        
        // 开始游戏
        this.game.startGame(this.selectedCharacter);
        
        // 隐藏菜单，显示游戏UI
        this.uiManager.showGameUI();
        
        // 发射事件
        window.gameEvents.emit('gameStarted', {
            character: this.selectedCharacter,
            level: this.selectedLevel
        });
    }
    
    pauseGame() {
        this.game.pauseGame();
        this.uiManager.showPauseMenu();
    }
    
    resumeGame() {
        this.game.resumeGame();
        this.uiManager.hidePauseMenu();
    }
    
    restartGame() {
        this.game.endGame();
        this.startGame();
    }
    
    returnToMenu() {
        this.game.endGame();
        this.uiManager.showMainMenu();
        
        // 停止音乐
        window.audioManager?.stopMusic();
    }
    
    onGameStarted(data) {
        console.log(`Game started with character: ${data.character}, level: ${data.level}`);
    }
    
    onGameEnded(data) {
        // 检查关卡完成
        const currentLevel = window.levelManager.getCurrentLevel();
        if (currentLevel) {
            const gameStats = this.game.getGameStats();
            const result = window.levelManager.completeLevel(currentLevel.id, gameStats);
            
            if (result) {
                this.onLevelCompleted(result);
            }
        }
        
        // 显示游戏结束界面
        this.uiManager.showGameOverScreen();
    }
    
    onLevelCompleted(data) {
        console.log(`Level completed: ${data.level.name}`);
        
        // 显示关卡完成奖励
        this.uiManager.showLevelCompletionRewards(data);
    }
    
    // 调试功能
    toggleDebug() {
        this.renderer.toggleDebug();
    }
    
    toggleParticles() {
        this.renderer.toggleParticles();
    }
    
    // 设置功能
    setSoundVolume(volume) {
        window.audioManager?.setSoundVolume(volume);
    }
    
    setMusicVolume(volume) {
        window.audioManager?.setMusicVolume(volume);
    }
    
    toggleMute() {
        return window.audioManager?.toggleMute();
    }
    
    // 数据管理
    saveGame() {
        this.game.saveGameData();
        window.achievementManager?.saveProgress();
        window.levelManager?.saveProgress();
    }
    
    loadGame() {
        // 游戏数据会在各个管理器初始化时自动加载
        console.log('Game data loaded');
    }
    
    resetGameData() {
        if (confirm('确定要重置所有游戏数据吗？此操作不可撤销！')) {
            // 清除所有存储的数据
            localStorage.removeItem('gameData');
            localStorage.removeItem('achievements');
            localStorage.removeItem('levelProgress');
            localStorage.removeItem('audioSettings');
            
            // 重新初始化管理器
            window.achievementManager?.resetProgress();
            window.levelManager?.resetProgress();
            
            // 重新加载页面
            location.reload();
        }
    }
    
    // 获取游戏状态
    getGameStatus() {
        return {
            isRunning: this.isRunning,
            gameState: this.game.state,
            selectedCharacter: this.selectedCharacter,
            selectedLevel: this.selectedLevel,
            score: this.game.score,
            level: this.game.level,
            fps: this.renderer.fps
        };
    }
}

// 输入处理器
class InputHandler {
    constructor(game, controller) {
        this.game = game;
        this.controller = controller;
        this.keys = new Set();
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => this.onKeyDown(e));
        document.addEventListener('keyup', (e) => this.onKeyUp(e));
        
        // 鼠标/触摸事件
        this.controller.canvas.addEventListener('click', (e) => this.onClick(e));
        this.controller.canvas.addEventListener('touchstart', (e) => this.onTouch(e));
        
        // 防止默认行为
        document.addEventListener('keydown', (e) => {
            if (['Space', 'ArrowUp', 'ArrowDown'].includes(e.code)) {
                e.preventDefault();
            }
        });
    }
    
    onKeyDown(e) {
        this.keys.add(e.code);
        
        switch (e.code) {
            case 'Space':
            case 'ArrowUp':
                this.handleJump();
                break;
            case 'KeyP':
                this.handlePause();
                break;
            case 'KeyR':
                if (this.game.state === 'gameOver') {
                    this.controller.restartGame();
                }
                break;
            case 'Escape':
                if (this.game.state === 'playing') {
                    this.controller.pauseGame();
                } else if (this.game.state === 'paused') {
                    this.controller.resumeGame();
                }
                break;
            case 'KeyM':
                this.controller.toggleMute();
                break;
            case 'F1':
                e.preventDefault();
                this.controller.toggleDebug();
                break;
        }
    }
    
    onKeyUp(e) {
        this.keys.delete(e.code);
    }
    
    onClick(e) {
        this.handleJump();
    }
    
    onTouch(e) {
        e.preventDefault();
        this.handleJump();
    }
    
    handleJump() {
        if (this.game.state === 'playing' && this.game.player) {
            this.game.player.jump();
        } else if (this.game.state === 'menu') {
            // 在菜单界面点击开始游戏
            this.controller.startGame();
        } else if (this.game.state === 'paused') {
            this.controller.resumeGame();
        }
    }
    
    handlePause() {
        if (this.game.state === 'playing') {
            this.controller.pauseGame();
        } else if (this.game.state === 'paused') {
            this.controller.resumeGame();
        }
    }
    
    isKeyPressed(keyCode) {
        return this.keys.has(keyCode);
    }
}

// UI管理器
class UIManager {
    constructor(game, controller) {
        this.game = game;
        this.controller = controller;
        
        this.setupUIEventListeners();
        this.initializeUI();
    }
    
    setupUIEventListeners() {
        // 开始游戏按钮
        const startBtn = DOMUtils.get('#startGame');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.controller.startGame();
            });
        }
        
        // 设置按钮
        const settingsBtn = DOMUtils.get('#settingsBtn');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                this.showSettings();
            });
        }
        
        // 成就按钮
        const achievementsBtn = DOMUtils.get('#achievementsBtn');
        if (achievementsBtn) {
            achievementsBtn.addEventListener('click', () => {
                this.showAchievements();
            });
        }
        
        // 角色选择
        this.setupCharacterSelection();
        
        // 关卡选择
        this.setupLevelSelection();
    }
    
    setupCharacterSelection() {
        const characterCards = document.querySelectorAll('.character-card');
        characterCards.forEach(card => {
            card.addEventListener('click', () => {
                const character = card.dataset.character;
                this.selectCharacter(character);
            });
        });
    }
    
    setupLevelSelection() {
        const levelCards = document.querySelectorAll('.level-card');
        levelCards.forEach(card => {
            card.addEventListener('click', () => {
                const level = card.dataset.level;
                this.selectLevel(level);
            });
        });
    }
    
    selectCharacter(character) {
        // 更新UI
        document.querySelectorAll('.character-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        const selectedCard = document.querySelector(`[data-character="${character}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }
        
        // 发射事件
        window.gameEvents.emit('characterSelected', character);
    }
    
    selectLevel(level) {
        // 更新UI
        document.querySelectorAll('.level-card').forEach(card => {
            card.classList.remove('selected');
        });
        
        const selectedCard = document.querySelector(`[data-level="${level}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
        }
        
        // 发射事件
        window.gameEvents.emit('levelSelected', level);
    }
    
    initializeUI() {
        this.showMainMenu();
        this.updateCharacterCards();
        this.updateLevelCards();
    }
    
    showMainMenu() {
        DOMUtils.show('#mainMenu');
        DOMUtils.hide('#gameUI');
        DOMUtils.hide('#pauseMenu');
        DOMUtils.hide('#gameOverScreen');
    }
    
    showGameUI() {
        DOMUtils.hide('#mainMenu');
        DOMUtils.show('#gameUI');
        DOMUtils.hide('#pauseMenu');
        DOMUtils.hide('#gameOverScreen');
    }
    
    showPauseMenu() {
        DOMUtils.show('#pauseMenu');
    }
    
    hidePauseMenu() {
        DOMUtils.hide('#pauseMenu');
    }
    
    showGameOverScreen() {
        DOMUtils.show('#gameOverScreen');
        this.updateGameOverStats();
    }
    
    showSettings() {
        DOMUtils.show('#settingsScreen');
    }
    
    showAchievements() {
        DOMUtils.show('#achievementsScreen');
        this.updateAchievementsList();
    }
    
    updateCharacterCards() {
        const characters = CharacterManager.getAllCharacters();
        const gameData = this.game.loadGameData();
        
        characters.forEach(character => {
            const card = document.querySelector(`[data-character="${character.type}"]`);
            if (card) {
                const isUnlocked = CharacterManager.isCharacterUnlocked(character.type, gameData);
                card.classList.toggle('locked', !isUnlocked);
            }
        });
    }
    
    updateLevelCards() {
        const levels = window.levelManager.getAllLevels();
        const gameData = this.game.loadGameData();
        
        levels.forEach(level => {
            const card = document.querySelector(`[data-level="${level.id}"]`);
            if (card) {
                const isUnlocked = level.isUnlocked(gameData);
                card.classList.toggle('locked', !isUnlocked);
            }
        });
    }
    
    updateGameOverStats() {
        const stats = this.game.getGameStats();
        
        // 更新最终分数
        const finalScore = DOMUtils.get('#finalScore');
        if (finalScore) finalScore.textContent = stats.currentScore;
        
        // 更新最高分
        const highScore = DOMUtils.get('#highScore');
        if (highScore) highScore.textContent = stats.highScore;
        
        // 更新其他统计
        const coinsEarned = DOMUtils.get('#coinsEarned');
        if (coinsEarned) coinsEarned.textContent = this.game.coins;
    }
    
    updateAchievementsList() {
        const achievements = window.achievementManager.getAllAchievements();
        const container = DOMUtils.get('#achievementsList');
        
        if (container) {
            container.innerHTML = '';
            
            achievements.forEach(achievement => {
                const achievementElement = this.createAchievementElement(achievement);
                container.appendChild(achievementElement);
            });
        }
    }
    
    createAchievementElement(achievement) {
        const element = DOMUtils.create('div', 'achievement-item');
        element.classList.toggle('unlocked', achievement.unlocked);
        
        element.innerHTML = `
            <div class="achievement-icon">${achievement.icon}</div>
            <div class="achievement-info">
                <h4>${achievement.name}</h4>
                <p>${achievement.description}</p>
                <div class="achievement-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${achievement.getProgressPercentage()}%"></div>
                    </div>
                    <span class="progress-text">${achievement.getProgressText()}</span>
                </div>
            </div>
        `;
        
        return element;
    }
    
    showLevelCompletionRewards(data) {
        // 显示关卡完成奖励的弹窗
        const modal = DOMUtils.create('div', 'level-completion-modal');
        modal.innerHTML = `
            <div class="modal-content">
                <h2>关卡完成！</h2>
                <h3>${data.level.name}</h3>
                <div class="rewards">
                    ${data.rewards.map(reward => `
                        <div class="reward-item">
                            <span class="reward-icon">${this.getRewardIcon(reward.type)}</span>
                            <span class="reward-text">${this.getRewardText(reward)}</span>
                        </div>
                    `).join('')}
                </div>
                <button onclick="this.parentElement.parentElement.remove()">继续</button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 自动移除
        setTimeout(() => modal.remove(), 5000);
    }
    
    getRewardIcon(type) {
        const icons = {
            experience: '⭐',
            character: '🐦',
            title: '🏆',
            coins: '💰'
        };
        return icons[type] || '🎁';
    }
    
    getRewardText(reward) {
        switch (reward.type) {
            case 'experience':
                return `获得 ${reward.amount} 经验值`;
            case 'character':
                return `解锁角色: ${reward.character}`;
            case 'title':
                return `获得称号: ${reward.title}`;
            case 'coins':
                return `获得 ${reward.amount} 金币`;
            default:
                return '神秘奖励';
        }
    }
    
    update() {
        // 更新实时UI元素
        if (this.game.state === 'playing') {
            this.updateGameStats();
        }
    }
    
    updateGameStats() {
        // 更新分数
        const scoreElement = DOMUtils.get('#currentScore');
        if (scoreElement) scoreElement.textContent = this.game.score;
        
        // 更新等级
        const levelElement = DOMUtils.get('#currentLevel');
        if (levelElement) levelElement.textContent = this.game.level;
        
        // 更新经验条
        const expFill = DOMUtils.get('#expFill');
        if (expFill) {
            const percentage = (this.game.experience / this.game.experienceToNext) * 100;
            expFill.style.width = `${percentage}%`;
        }
    }
}

// 初始化游戏
window.gameController = new GameController();
