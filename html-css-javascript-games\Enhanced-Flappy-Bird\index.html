<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>超级Flappy Bird - 增强版</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 游戏容器 -->
    <div id="gameContainer">
        <!-- 主菜单 -->
        <div id="mainMenu" class="screen">
            <div class="menu-content">
                <h1 class="game-title">超级 Flappy Bird</h1>
                <div class="menu-buttons">
                    <button id="startBtn" class="menu-btn primary">开始游戏</button>
                    <button id="characterBtn" class="menu-btn">选择角色</button>
                    <button id="achievementsBtn" class="menu-btn">成就</button>
                    <button id="settingsBtn" class="menu-btn">设置</button>
                </div>
                <div class="stats-preview">
                    <div class="stat">
                        <span class="stat-label">最高分</span>
                        <span id="highScore" class="stat-value">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">等级</span>
                        <span id="playerLevel" class="stat-value">1</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 角色选择界面 -->
        <div id="characterMenu" class="screen hidden">
            <div class="menu-content">
                <h2>选择你的鸟</h2>
                <div id="characterGrid" class="character-grid">
                    <!-- 角色将通过JavaScript动态生成 -->
                </div>
                <button id="backToMainBtn" class="menu-btn">返回</button>
            </div>
        </div>

        <!-- 游戏画布 -->
        <canvas id="gameCanvas" width="800" height="600"></canvas>

        <!-- 游戏UI覆盖层 -->
        <div id="gameUI" class="hidden">
            <div class="top-ui">
                <div class="score-display">
                    <span id="currentScore">0</span>
                </div>
                <div class="level-display">
                    <span>Lv.</span><span id="currentLevel">1</span>
                </div>
                <div class="exp-bar">
                    <div id="expFill" class="exp-fill"></div>
                </div>
            </div>
            
            <div class="power-ups">
                <div id="shieldPowerUp" class="power-up hidden">
                    <span class="power-up-icon">🛡️</span>
                    <span id="shieldTimer" class="power-up-timer">5</span>
                </div>
                <div id="slowMotionPowerUp" class="power-up hidden">
                    <span class="power-up-icon">⏰</span>
                    <span id="slowMotionTimer" class="power-up-timer">3</span>
                </div>
            </div>

            <button id="pauseBtn" class="pause-btn">⏸️</button>
        </div>

        <!-- 暂停菜单 -->
        <div id="pauseMenu" class="screen hidden">
            <div class="menu-content">
                <h2>游戏暂停</h2>
                <div class="menu-buttons">
                    <button id="resumeBtn" class="menu-btn primary">继续游戏</button>
                    <button id="restartBtn" class="menu-btn">重新开始</button>
                    <button id="mainMenuBtn" class="menu-btn">主菜单</button>
                </div>
            </div>
        </div>

        <!-- 游戏结束界面 -->
        <div id="gameOverMenu" class="screen hidden">
            <div class="menu-content">
                <h2 class="game-over-title">游戏结束</h2>
                <div class="final-stats">
                    <div class="stat">
                        <span class="stat-label">得分</span>
                        <span id="finalScore" class="stat-value">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">最高分</span>
                        <span id="finalHighScore" class="stat-value">0</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">获得经验</span>
                        <span id="expGained" class="stat-value">+0</span>
                    </div>
                </div>
                <div id="newAchievements" class="achievements-unlocked hidden">
                    <h3>新成就解锁！</h3>
                    <div id="achievementsList"></div>
                </div>
                <div class="menu-buttons">
                    <button id="playAgainBtn" class="menu-btn primary">再玩一次</button>
                    <button id="backToMenuBtn" class="menu-btn">主菜单</button>
                </div>
            </div>
        </div>

        <!-- 成就界面 -->
        <div id="achievementsMenu" class="screen hidden">
            <div class="menu-content">
                <h2>成就系统</h2>
                <div id="achievementsContainer" class="achievements-container">
                    <!-- 成就将通过JavaScript动态生成 -->
                </div>
                <button id="backFromAchievementsBtn" class="menu-btn">返回</button>
            </div>
        </div>

        <!-- 设置界面 -->
        <div id="settingsMenu" class="screen hidden">
            <div class="menu-content">
                <h2>游戏设置</h2>
                <div class="settings-options">
                    <div class="setting-item">
                        <label for="soundVolume">音效音量</label>
                        <input type="range" id="soundVolume" min="0" max="100" value="70">
                        <span id="soundVolumeValue">70%</span>
                    </div>
                    <div class="setting-item">
                        <label for="musicVolume">背景音乐</label>
                        <input type="range" id="musicVolume" min="0" max="100" value="50">
                        <span id="musicVolumeValue">50%</span>
                    </div>
                    <div class="setting-item">
                        <label for="difficulty">难度</label>
                        <select id="difficulty">
                            <option value="easy">简单</option>
                            <option value="normal" selected>普通</option>
                            <option value="hard">困难</option>
                            <option value="extreme">极限</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="particleEffects">粒子效果</label>
                        <input type="checkbox" id="particleEffects" checked>
                    </div>
                </div>
                <button id="backFromSettingsBtn" class="menu-btn">返回</button>
            </div>
        </div>
    </div>

    <!-- 粒子效果容器 -->
    <div id="particleContainer"></div>

    <!-- 音频元素 -->
    <audio id="bgMusic" loop>
        <source src="sounds/background.mp3" type="audio/mpeg">
    </audio>
    <audio id="jumpSound">
        <source src="sounds/jump.mp3" type="audio/mpeg">
    </audio>
    <audio id="scoreSound">
        <source src="sounds/score.mp3" type="audio/mpeg">
    </audio>
    <audio id="powerUpSound">
        <source src="sounds/powerup.mp3" type="audio/mpeg">
    </audio>
    <audio id="gameOverSound">
        <source src="sounds/gameover.mp3" type="audio/mpeg">
    </audio>

    <!-- JavaScript文件 -->
    <script src="js/utils.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/characters.js"></script>
    <script src="js/powerups.js"></script>
    <script src="js/achievements.js"></script>
    <script src="js/audio.js"></script>
    <script src="js/game.js"></script>
    <script src="js/renderer.js"></script>
    <script src="js/levels.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
