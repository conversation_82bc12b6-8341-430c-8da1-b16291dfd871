// 成就系统

class Achievement {
    constructor(id, config) {
        this.id = id;
        this.name = config.name;
        this.description = config.description;
        this.icon = config.icon;
        this.condition = config.condition;
        this.reward = config.reward;
        this.unlocked = false;
        this.progress = 0;
        this.maxProgress = config.condition.target || 1;
        this.category = config.category || 'general';
        this.rarity = config.rarity || 'common';
        this.unlockedAt = null;
    }
    
    checkProgress(gameStats) {
        if (this.unlocked) return false;
        
        const oldProgress = this.progress;
        
        switch (this.condition.type) {
            case 'score':
                this.progress = Math.max(this.progress, gameStats.currentScore || 0);
                break;
                
            case 'highScore':
                this.progress = Math.max(this.progress, gameStats.highScore || 0);
                break;
                
            case 'totalScore':
                this.progress = gameStats.totalScore || 0;
                break;
                
            case 'gamesPlayed':
                this.progress = gameStats.gamesPlayed || 0;
                break;
                
            case 'coinsCollected':
                this.progress = gameStats.totalCoins || 0;
                break;
                
            case 'powerUpsUsed':
                this.progress = gameStats.powerUpsUsed || 0;
                break;
                
            case 'timeAlive':
                this.progress = Math.max(this.progress, gameStats.currentTimeAlive || 0);
                break;
                
            case 'consecutiveJumps':
                this.progress = Math.max(this.progress, gameStats.consecutiveJumps || 0);
                break;
                
            case 'perfectRuns':
                this.progress = gameStats.perfectRuns || 0;
                break;
                
            case 'characterUnlocked':
                this.progress = gameStats.charactersUnlocked || 0;
                break;
        }
        
        // 检查是否达成成就
        if (this.progress >= this.maxProgress && !this.unlocked) {
            this.unlock();
            return true;
        }
        
        // 检查进度是否有更新
        return this.progress > oldProgress;
    }
    
    unlock() {
        this.unlocked = true;
        this.unlockedAt = new Date();
        
        // 应用奖励
        this.applyReward();
        
        // 发射事件
        window.gameEvents?.emit('achievementUnlocked', this);
        
        // 显示通知
        this.showUnlockNotification();
    }
    
    applyReward() {
        if (!this.reward) return;
        
        switch (this.reward.type) {
            case 'experience':
                window.gameEvents?.emit('experienceGained', this.reward.amount);
                break;
                
            case 'coins':
                window.gameEvents?.emit('coinsGained', this.reward.amount);
                break;
                
            case 'character':
                CharacterManager.unlockCharacter(this.reward.character);
                break;
                
            case 'title':
                window.gameEvents?.emit('titleUnlocked', this.reward.title);
                break;
        }
    }
    
    showUnlockNotification() {
        // 创建成就解锁通知
        const notification = DOMUtils.create('div', 'achievement-notification');
        notification.innerHTML = `
            <div class="achievement-icon">${this.icon}</div>
            <div class="achievement-info">
                <h4>成就解锁！</h4>
                <p>${this.name}</p>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 动画显示
        setTimeout(() => notification.classList.add('show'), 100);
        
        // 自动移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 500);
        }, 3000);
    }
    
    getProgressPercentage() {
        return Math.min((this.progress / this.maxProgress) * 100, 100);
    }
    
    getProgressText() {
        if (this.unlocked) return '已完成';
        return `${this.progress}/${this.maxProgress}`;
    }
}

class AchievementManager {
    constructor() {
        this.achievements = new Map();
        this.initializeAchievements();
        this.loadProgress();
    }
    
    initializeAchievements() {
        const achievementConfigs = {
            // 得分相关
            firstScore: {
                name: '初次飞行',
                description: '获得第一分',
                icon: '🎯',
                condition: { type: 'score', target: 1 },
                reward: { type: 'experience', amount: 10 },
                category: 'score',
                rarity: 'common'
            },
            
            score10: {
                name: '新手飞行员',
                description: '单局得分达到10分',
                icon: '🏆',
                condition: { type: 'score', target: 10 },
                reward: { type: 'experience', amount: 25 },
                category: 'score',
                rarity: 'common'
            },
            
            score25: {
                name: '熟练飞行员',
                description: '单局得分达到25分',
                icon: '🥉',
                condition: { type: 'score', target: 25 },
                reward: { type: 'character', character: 'ninja' },
                category: 'score',
                rarity: 'rare'
            },
            
            score50: {
                name: '王牌飞行员',
                description: '单局得分达到50分',
                icon: '🥈',
                condition: { type: 'score', target: 50 },
                reward: { type: 'character', character: 'rainbow' },
                category: 'score',
                rarity: 'epic'
            },
            
            score100: {
                name: '传奇飞行员',
                description: '单局得分达到100分',
                icon: '🥇',
                condition: { type: 'score', target: 100 },
                reward: { type: 'character', character: 'phoenix' },
                category: 'score',
                rarity: 'legendary'
            },
            
            // 金币相关
            coinCollector: {
                name: '收藏家',
                description: '收集100枚金币',
                icon: '💰',
                condition: { type: 'coinsCollected', target: 100 },
                reward: { type: 'experience', amount: 50 },
                category: 'collection',
                rarity: 'common'
            },
            
            coinMaster: {
                name: '金币大师',
                description: '收集1000枚金币',
                icon: '💎',
                condition: { type: 'coinsCollected', target: 1000 },
                reward: { type: 'experience', amount: 200 },
                category: 'collection',
                rarity: 'epic'
            },
            
            // 游戏次数相关
            persistent: {
                name: '坚持不懈',
                description: '游玩10局游戏',
                icon: '🎮',
                condition: { type: 'gamesPlayed', target: 10 },
                reward: { type: 'experience', amount: 30 },
                category: 'persistence',
                rarity: 'common'
            },
            
            dedicated: {
                name: '专注玩家',
                description: '游玩50局游戏',
                icon: '🎯',
                condition: { type: 'gamesPlayed', target: 50 },
                reward: { type: 'experience', amount: 100 },
                category: 'persistence',
                rarity: 'rare'
            },
            
            // 道具相关
            powerUpUser: {
                name: '道具使用者',
                description: '使用25个道具',
                icon: '⚡',
                condition: { type: 'powerUpsUsed', target: 25 },
                reward: { type: 'experience', amount: 40 },
                category: 'powerups',
                rarity: 'common'
            },
            
            // 生存时间相关
            survivor: {
                name: '生存专家',
                description: '单局存活60秒',
                icon: '⏱️',
                condition: { type: 'timeAlive', target: 60000 },
                reward: { type: 'experience', amount: 75 },
                category: 'survival',
                rarity: 'rare'
            },
            
            // 特殊成就
            perfectRun: {
                name: '完美飞行',
                description: '不使用道具达到20分',
                icon: '✨',
                condition: { type: 'perfectRuns', target: 1 },
                reward: { type: 'experience', amount: 150 },
                category: 'special',
                rarity: 'epic'
            },
            
            jumpMaster: {
                name: '跳跃大师',
                description: '连续跳跃100次不撞墙',
                icon: '🦘',
                condition: { type: 'consecutiveJumps', target: 100 },
                reward: { type: 'experience', amount: 80 },
                category: 'skill',
                rarity: 'rare'
            },
            
            characterCollector: {
                name: '角色收集者',
                description: '解锁所有角色',
                icon: '🎭',
                condition: { type: 'characterUnlocked', target: 5 },
                reward: { type: 'title', title: '收集大师' },
                category: 'collection',
                rarity: 'legendary'
            }
        };
        
        // 创建成就实例
        Object.entries(achievementConfigs).forEach(([id, config]) => {
            this.achievements.set(id, new Achievement(id, config));
        });
    }
    
    updateProgress(gameStats) {
        const newlyUnlocked = [];
        
        this.achievements.forEach(achievement => {
            if (achievement.checkProgress(gameStats)) {
                newlyUnlocked.push(achievement);
            }
        });
        
        if (newlyUnlocked.length > 0) {
            this.saveProgress();
        }
        
        return newlyUnlocked;
    }
    
    getAchievement(id) {
        return this.achievements.get(id);
    }
    
    getAllAchievements() {
        return Array.from(this.achievements.values());
    }
    
    getAchievementsByCategory(category) {
        return this.getAllAchievements().filter(achievement => 
            achievement.category === category
        );
    }
    
    getUnlockedAchievements() {
        return this.getAllAchievements().filter(achievement => 
            achievement.unlocked
        );
    }
    
    getUnlockedCount() {
        return this.getUnlockedAchievements().length;
    }
    
    getTotalCount() {
        return this.achievements.size;
    }
    
    getCompletionPercentage() {
        return (this.getUnlockedCount() / this.getTotalCount()) * 100;
    }
    
    saveProgress() {
        const data = {};
        this.achievements.forEach((achievement, id) => {
            data[id] = {
                unlocked: achievement.unlocked,
                progress: achievement.progress,
                unlockedAt: achievement.unlockedAt
            };
        });
        
        StorageUtils.save('achievements', data);
    }
    
    loadProgress() {
        const data = StorageUtils.load('achievements', {});
        
        this.achievements.forEach((achievement, id) => {
            if (data[id]) {
                achievement.unlocked = data[id].unlocked || false;
                achievement.progress = data[id].progress || 0;
                achievement.unlockedAt = data[id].unlockedAt ? 
                    new Date(data[id].unlockedAt) : null;
            }
        });
    }
    
    resetProgress() {
        this.achievements.forEach(achievement => {
            achievement.unlocked = false;
            achievement.progress = 0;
            achievement.unlockedAt = null;
        });
        
        this.saveProgress();
    }
    
    // 获取成就统计
    getStats() {
        const categories = {};
        const rarities = {};
        
        this.getAllAchievements().forEach(achievement => {
            // 按类别统计
            if (!categories[achievement.category]) {
                categories[achievement.category] = { total: 0, unlocked: 0 };
            }
            categories[achievement.category].total++;
            if (achievement.unlocked) {
                categories[achievement.category].unlocked++;
            }
            
            // 按稀有度统计
            if (!rarities[achievement.rarity]) {
                rarities[achievement.rarity] = { total: 0, unlocked: 0 };
            }
            rarities[achievement.rarity].total++;
            if (achievement.unlocked) {
                rarities[achievement.rarity].unlocked++;
            }
        });
        
        return {
            total: this.getTotalCount(),
            unlocked: this.getUnlockedCount(),
            completion: this.getCompletionPercentage(),
            categories,
            rarities
        };
    }
}

// 全局成就管理器实例
window.achievementManager = new AchievementManager();
