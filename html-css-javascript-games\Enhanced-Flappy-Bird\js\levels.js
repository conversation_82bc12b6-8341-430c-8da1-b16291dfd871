// 关卡系统

class Level {
    constructor(id, config) {
        this.id = id;
        this.name = config.name;
        this.description = config.description;
        this.theme = config.theme;
        this.difficulty = config.difficulty;
        this.unlockCondition = config.unlockCondition;
        this.unlocked = config.unlocked || false;
        
        // 关卡参数
        this.pipeSpeed = config.pipeSpeed || 200;
        this.pipeGap = config.pipeGap || 150;
        this.pipeSpacing = config.pipeSpacing || 300;
        this.gravity = config.gravity || 800;
        this.powerUpChance = config.powerUpChance || 0.3;
        this.coinChance = config.coinChance || 0.6;
        
        // 特殊机制
        this.specialMechanics = config.specialMechanics || [];
        this.backgroundMusic = config.backgroundMusic || 'background';
        this.colorScheme = config.colorScheme || 'default';
        
        // 目标和奖励
        this.objectives = config.objectives || [];
        this.rewards = config.rewards || [];
        
        // 环境效果
        this.environmentEffects = config.environmentEffects || [];
    }
    
    applyToGame(game) {
        // 应用关卡设置到游戏
        game.pipeSpeed = this.pipeSpeed;
        game.pipeGap = this.pipeGap;
        game.pipeSpacing = this.pipeSpacing;
        game.powerUpChance = this.powerUpChance;
        game.coinChance = this.coinChance;
        
        // 应用特殊机制
        this.specialMechanics.forEach(mechanic => {
            this.applySpecialMechanic(game, mechanic);
        });
        
        // 应用环境效果
        this.environmentEffects.forEach(effect => {
            this.applyEnvironmentEffect(game, effect);
        });
        
        // 播放背景音乐
        window.audioManager?.playMusic(this.backgroundMusic);
    }
    
    applySpecialMechanic(game, mechanic) {
        switch (mechanic.type) {
            case 'movingPipes':
                game.movingPipes = true;
                game.pipeMovementSpeed = mechanic.speed || 50;
                break;
                
            case 'windEffect':
                game.windForce = mechanic.force || 100;
                game.windDirection = mechanic.direction || 'horizontal';
                break;
                
            case 'gravityChange':
                game.alternateGravity = true;
                game.gravityChangeInterval = mechanic.interval || 5000;
                break;
                
            case 'invisiblePipes':
                game.invisiblePipeChance = mechanic.chance || 0.2;
                break;
                
            case 'multipleCharacters':
                game.enableMultipleCharacters = true;
                game.characterCount = mechanic.count || 2;
                break;
        }
    }
    
    applyEnvironmentEffect(game, effect) {
        switch (effect.type) {
            case 'rain':
                game.particleSystem.enableRain(effect.intensity || 0.5);
                break;
                
            case 'snow':
                game.particleSystem.enableSnow(effect.intensity || 0.3);
                break;
                
            case 'fog':
                game.renderer.enableFog(effect.density || 0.3);
                break;
                
            case 'lightning':
                game.enableLightning = true;
                game.lightningInterval = effect.interval || 10000;
                break;
        }
    }
    
    checkObjectives(gameStats) {
        const completedObjectives = [];
        
        this.objectives.forEach(objective => {
            if (this.isObjectiveCompleted(objective, gameStats)) {
                completedObjectives.push(objective);
            }
        });
        
        return completedObjectives;
    }
    
    isObjectiveCompleted(objective, gameStats) {
        switch (objective.type) {
            case 'score':
                return gameStats.score >= objective.target;
            case 'survive':
                return gameStats.timeAlive >= objective.target;
            case 'collectCoins':
                return gameStats.coinsCollected >= objective.target;
            case 'usePowerUps':
                return gameStats.powerUpsUsed >= objective.target;
            case 'perfectRun':
                return gameStats.perfectRun && gameStats.score >= objective.minScore;
            default:
                return false;
        }
    }
    
    unlock() {
        this.unlocked = true;
        
        // 发射解锁事件
        window.gameEvents?.emit('levelUnlocked', this);
        
        // 保存解锁状态
        LevelManager.saveLevelProgress();
    }
    
    isUnlocked(playerStats) {
        if (this.unlocked) return true;
        if (!this.unlockCondition) return true;
        
        switch (this.unlockCondition.type) {
            case 'score':
                return playerStats.highScore >= this.unlockCondition.value;
            case 'level':
                return playerStats.level >= this.unlockCondition.value;
            case 'completePreviousLevel':
                return LevelManager.isLevelCompleted(this.unlockCondition.levelId);
            case 'achievement':
                return playerStats.achievements.includes(this.unlockCondition.achievementId);
            default:
                return false;
        }
    }
}

class LevelManager {
    constructor() {
        this.levels = new Map();
        this.currentLevel = null;
        this.completedLevels = new Set();
        
        this.initializeLevels();
        this.loadProgress();
    }
    
    initializeLevels() {
        const levelConfigs = {
            tutorial: {
                name: '新手教程',
                description: '学习基本操作',
                theme: 'peaceful',
                difficulty: 'easy',
                unlocked: true,
                pipeSpeed: 150,
                pipeGap: 200,
                pipeSpacing: 400,
                powerUpChance: 0.5,
                coinChance: 0.8,
                objectives: [
                    { type: 'score', target: 5, description: '获得5分' }
                ],
                rewards: [
                    { type: 'experience', amount: 50 }
                ]
            },
            
            classic: {
                name: '经典模式',
                description: '原版Flappy Bird体验',
                theme: 'classic',
                difficulty: 'normal',
                unlockCondition: { type: 'score', value: 5 },
                pipeSpeed: 200,
                pipeGap: 150,
                pipeSpacing: 300,
                powerUpChance: 0.3,
                coinChance: 0.6,
                objectives: [
                    { type: 'score', target: 20, description: '获得20分' },
                    { type: 'perfectRun', minScore: 10, description: '不使用道具获得10分' }
                ],
                rewards: [
                    { type: 'experience', amount: 100 },
                    { type: 'character', character: 'rocket' }
                ]
            },
            
            windy: {
                name: '风暴挑战',
                description: '在强风中飞行',
                theme: 'storm',
                difficulty: 'hard',
                unlockCondition: { type: 'score', value: 20 },
                pipeSpeed: 220,
                pipeGap: 140,
                pipeSpacing: 280,
                powerUpChance: 0.4,
                coinChance: 0.5,
                specialMechanics: [
                    { type: 'windEffect', force: 150, direction: 'horizontal' }
                ],
                environmentEffects: [
                    { type: 'rain', intensity: 0.7 }
                ],
                objectives: [
                    { type: 'score', target: 30, description: '在风暴中获得30分' },
                    { type: 'survive', target: 60000, description: '存活60秒' }
                ],
                rewards: [
                    { type: 'experience', amount: 200 },
                    { type: 'character', character: 'ninja' }
                ]
            },
            
            moving: {
                name: '动态管道',
                description: '管道会上下移动',
                theme: 'mechanical',
                difficulty: 'hard',
                unlockCondition: { type: 'score', value: 30 },
                pipeSpeed: 200,
                pipeGap: 160,
                pipeSpacing: 320,
                powerUpChance: 0.35,
                coinChance: 0.7,
                specialMechanics: [
                    { type: 'movingPipes', speed: 80 }
                ],
                objectives: [
                    { type: 'score', target: 25, description: '适应动态管道获得25分' }
                ],
                rewards: [
                    { type: 'experience', amount: 150 }
                ]
            },
            
            gravity: {
                name: '重力反转',
                description: '重力会定期反转',
                theme: 'space',
                difficulty: 'extreme',
                unlockCondition: { type: 'score', value: 40 },
                pipeSpeed: 180,
                pipeGap: 170,
                pipeSpacing: 350,
                powerUpChance: 0.4,
                coinChance: 0.6,
                specialMechanics: [
                    { type: 'gravityChange', interval: 8000 }
                ],
                environmentEffects: [
                    { type: 'lightning', interval: 15000 }
                ],
                objectives: [
                    { type: 'score', target: 35, description: '在重力反转中获得35分' },
                    { type: 'collectCoins', target: 50, description: '收集50枚金币' }
                ],
                rewards: [
                    { type: 'experience', amount: 300 },
                    { type: 'character', character: 'rainbow' }
                ]
            },
            
            invisible: {
                name: '隐形挑战',
                description: '部分管道会隐形',
                theme: 'mystery',
                difficulty: 'extreme',
                unlockCondition: { type: 'score', value: 50 },
                pipeSpeed: 200,
                pipeGap: 150,
                pipeSpacing: 300,
                powerUpChance: 0.5,
                coinChance: 0.4,
                specialMechanics: [
                    { type: 'invisiblePipes', chance: 0.3 }
                ],
                environmentEffects: [
                    { type: 'fog', density: 0.4 }
                ],
                objectives: [
                    { type: 'score', target: 40, description: '在隐形管道中获得40分' }
                ],
                rewards: [
                    { type: 'experience', amount: 400 }
                ]
            },
            
            nightmare: {
                name: '噩梦模式',
                description: '终极挑战',
                theme: 'nightmare',
                difficulty: 'nightmare',
                unlockCondition: { type: 'level', value: 10 },
                pipeSpeed: 250,
                pipeGap: 120,
                pipeSpacing: 250,
                powerUpChance: 0.6,
                coinChance: 0.3,
                specialMechanics: [
                    { type: 'movingPipes', speed: 100 },
                    { type: 'windEffect', force: 200, direction: 'both' },
                    { type: 'invisiblePipes', chance: 0.2 }
                ],
                environmentEffects: [
                    { type: 'lightning', interval: 8000 },
                    { type: 'fog', density: 0.2 }
                ],
                objectives: [
                    { type: 'score', target: 50, description: '在噩梦模式中获得50分' },
                    { type: 'survive', target: 120000, description: '存活2分钟' }
                ],
                rewards: [
                    { type: 'experience', amount: 500 },
                    { type: 'character', character: 'phoenix' },
                    { type: 'title', title: '噩梦征服者' }
                ]
            }
        };
        
        // 创建关卡实例
        Object.entries(levelConfigs).forEach(([id, config]) => {
            this.levels.set(id, new Level(id, config));
        });
    }
    
    getLevel(id) {
        return this.levels.get(id);
    }
    
    getAllLevels() {
        return Array.from(this.levels.values());
    }
    
    getUnlockedLevels(playerStats) {
        return this.getAllLevels().filter(level => 
            level.isUnlocked(playerStats)
        );
    }
    
    setCurrentLevel(levelId) {
        const level = this.getLevel(levelId);
        if (level) {
            this.currentLevel = level;
            return true;
        }
        return false;
    }
    
    getCurrentLevel() {
        return this.currentLevel;
    }
    
    completeLevel(levelId, gameStats) {
        const level = this.getLevel(levelId);
        if (!level) return false;
        
        this.completedLevels.add(levelId);
        
        // 检查目标完成情况
        const completedObjectives = level.checkObjectives(gameStats);
        
        // 应用奖励
        level.rewards.forEach(reward => {
            this.applyReward(reward);
        });
        
        // 解锁下一个关卡
        this.checkLevelUnlocks(gameStats);
        
        // 保存进度
        this.saveProgress();
        
        return {
            level: level,
            completedObjectives: completedObjectives,
            rewards: level.rewards
        };
    }
    
    applyReward(reward) {
        switch (reward.type) {
            case 'experience':
                window.gameEvents?.emit('experienceGained', reward.amount);
                break;
            case 'character':
                CharacterManager.unlockCharacter(reward.character);
                break;
            case 'title':
                window.gameEvents?.emit('titleUnlocked', reward.title);
                break;
        }
    }
    
    checkLevelUnlocks(playerStats) {
        this.getAllLevels().forEach(level => {
            if (!level.unlocked && level.isUnlocked(playerStats)) {
                level.unlock();
            }
        });
    }
    
    isLevelCompleted(levelId) {
        return this.completedLevels.has(levelId);
    }
    
    getCompletionStats() {
        const total = this.levels.size;
        const completed = this.completedLevels.size;
        return {
            total: total,
            completed: completed,
            percentage: (completed / total) * 100
        };
    }
    
    saveProgress() {
        const data = {
            completedLevels: Array.from(this.completedLevels),
            unlockedLevels: this.getAllLevels()
                .filter(level => level.unlocked)
                .map(level => level.id)
        };
        
        StorageUtils.save('levelProgress', data);
    }
    
    loadProgress() {
        const data = StorageUtils.load('levelProgress', {
            completedLevels: [],
            unlockedLevels: ['tutorial']
        });
        
        this.completedLevels = new Set(data.completedLevels);
        
        // 解锁已保存的关卡
        data.unlockedLevels.forEach(levelId => {
            const level = this.getLevel(levelId);
            if (level) {
                level.unlocked = true;
            }
        });
    }
    
    resetProgress() {
        this.completedLevels.clear();
        this.getAllLevels().forEach(level => {
            level.unlocked = level.id === 'tutorial';
        });
        this.saveProgress();
    }
}

// 全局关卡管理器实例
window.levelManager = new LevelManager();
