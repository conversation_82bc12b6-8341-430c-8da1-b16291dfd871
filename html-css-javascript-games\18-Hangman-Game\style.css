@import url("https://fonts.googleapis.com/css2?family=DotGothic16&display=swap");

:root {
  --primary-color: #1f2f61;
  --secondary-color: #224ca4;
  --light-color: #a7c2da;
}

* {
  box-sizing: border-box;
}

body {
  background-color: var(--primary-color);
  color: var(--light-color);
  font-family: "DotGothic16", sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80vh;
  overflow: hidden;
  margin: 0;
}

h1 {
  margin: 20px 0 0;
  letter-spacing: 0.5rem;
  text-transform: uppercase;
}

h2,
h3 {
  letter-spacing: 0.2rem;
}

.game-container {
  padding: 20px 30px;
  position: relative;
  margin: auto;
  height: 350px;
  width: 450px;
}

.figure-container {
  fill: transparent;
  stroke: var(--light-color);
  stroke-width: 4px;
  stroke-linecap: round;
}

.figure-part {
  display: none;
}

.wrong-letters-container {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  text-align: right;
}

.wrong-letters-container p {
  margin: 0 0 5px;
}

.wrong-letters-container span {
  font-size: 24px;
}

.word {
  display: flex;
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
}

.letter {
  border-bottom: 3px solid var(--secondary-color);
  display: inline-flex;
  font-size: 30px;
  align-items: center;
  justify-content: center;
  margin: 0 3px;
  height: 50px;
  width: 20px;
}

.popup-container {
  background-color: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: none;
  align-items: center;
  justify-content: center;
}

.popup {
  background-color: var(--secondary-color);
  border-radius: 5px;
  box-shadow: 0 15px 10px 3px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: center;
}

.popup button {
  cursor: pointer;
  background-color: var(--light-color);
  color: var(--secondary-color);
  border: 0;
  margin-top: 20px;
  padding: 12px 20px;
  font-size: 16px;
  font-family: inherit;
  border-radius: 5px;
}

.popup button:active {
  transform: scale(0.98);
}

.popup button:focus {
  outline: none;
}

.notification-container {
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 10px 10px 0 0;
  padding: 15px 20px;
  position: absolute;
  bottom: -60px;
  transition: transform 0.3s ease-in-out;
}

.notification-container.show {
  transform: translateY(-60px);
}

.notification-container p {
  margin: 0;
}
