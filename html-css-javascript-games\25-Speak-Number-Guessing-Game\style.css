@import url("https://fonts.googleapis.com/css2?family=Recursive:wght@400;700&display=swap");

* {
  box-sizing: border-box;
}

body {
  background: #27273d
    url("https://images.unsplash.com/photo-1552749412-091909ed9f82?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80")
    no-repeat center center/cover;
  color: rgba(255, 255, 255, 0.87);
  font-family: "Recursive", sans-serif;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100vh;
  overflow: hidden;
  margin: 0;
}

body::after {
  content: "";
  background-color: rgba(0, 0, 0, 0.7);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

body * {
  z-index: 1;
}

img {
  width: 8rem;
  height: 8rem;
}

h1,
h2 {
  margin-bottom: 0;
}

h2 {
  font-size: 1.17rem;
}

p {
  line-height: 1.5;
  margin: 0;
}

.play-again {
  cursor: pointer;
  padding: 0.5rem 1rem;
  border: 0;
  background-color: #f4f4f4;
  border-radius: 5px;
  margin-top: 2rem;
}

.play-again:hover {
  background-color: #27273d;
  color: #f4f4f4;
}

.play-again:focus {
  outline: none;
}

.msg {
  font-size: 1.5rem;
  margin-top: 2.5rem;
}

.box {
  border: 1px solid #dedede;
  display: inline-block;
  font-size: 1.875rem;
  margin: 1.25rem;
  padding: 0.625rem;
}
