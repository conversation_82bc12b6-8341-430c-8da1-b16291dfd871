@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  min-height: 100vh;
  background: #db3069;
}

h1 {
  font-size: 24px;
  margin-bottom: 20px;
  padding: 10px 50px;
  color: #1446a0;
  background-color: white;
  border-radius: 50px;
}

.container {
  text-align: center;
  padding: 25px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
}
.container h2 {
  font-size: 20px;
  color: #1446a0;
  font-weight: 500;
}

.container .guess {
  color: #1446a0;
  font-size: 18px;
  margin: 10px 0;
}

.container .chances {
  color: black;
  font-size: 16px;
  margin-top: 5px;
}

.container input {
  font-size: 20px;
  padding: 0 20px;
  text-align: center;
  width: 100%;
  height: 50px;
  margin: 10px 0;
  border: 3px solid #1446a0;
  border-radius: 5px;
}

input:disabled {
  cursor: not-allowed;
}

/* To remove the controls in the input field */
/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.checkBtn {
  font-size: 18px;
  margin: 10px 0;
  padding: 10px 40px;
  color: white;
  background: #1446a0;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.checkBtn:active {
  transform: scale(0.95);
}
