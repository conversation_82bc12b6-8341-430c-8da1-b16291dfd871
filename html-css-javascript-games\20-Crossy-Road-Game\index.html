<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Talha - Crossy Road Game Clone</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="stylesheet" href="style.css" />
    <link rel="icon" href="https://i.ibb.co/M6KTWnf/pic.jpg" />

    <!-- 👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻-->
    <!-- Also uploaded the demo of this code in a gif : https://c.tenor.com/x8v1oNUOmg4AAAAd/tenor.gif-->
    <!-- 👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻-->

    <!-- 👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻-->
    <!-- More html-css-js Games Calculators Games Cards Elements Projects on https://www.github.com/he-is-talha -->
    <!-- 👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻-->
  </head>
  <body>
    <div id="counter">0</div>

    <div id="controlls">
      <div>
        <button id="forward">
          <svg width="30" height="30" viewBox="0 0 10 10">
            <g transform="rotate(0, 5,5)">
              <path d="M5,4 L7,6 L3,6 L5,4" />
            </g>
          </svg>
        </button>
        <button id="left">
          <svg width="30" height="30" viewBox="0 0 10 10">
            <g transform="rotate(-90, 5,5)">
              <path d="M5,4 L7,6 L3,6 L5,4" />
            </g>
          </svg>
        </button>
        <button id="backward">
          <svg width="30" height="30" viewBox="0 0 10 10">
            <g transform="rotate(180, 5,5)">
              <path d="M5,4 L7,6 L3,6 L5,4" />
            </g>
          </svg>
        </button>
        <button id="right">
          <svg width="30" height="30" viewBox="0 0 10 10">
            <g transform="rotate(90, 5,5)">
              <path d="M5,4 L7,6 L3,6 L5,4" />
            </g>
          </svg>
        </button>
      </div>
    </div>

    <div id="end">
      <button id="retry">Retry</button>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/99/three.min.js"></script>
    <script src="script.js"></script>
  </body>
</html>
