# HTML/CSS and JavaScript Games 🎮

This repository contains a collection of HTML, CSS, and JavaScript games. 🎯🕹

|  #  | Game                                                                                                                           | Live Demo                                                                                           |
| :-: | ------------------------------------------------------------------------------------------------------------------------------ | --------------------------------------------------------------------------------------------------- |
| 01  | [Candy Crush Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/01-<PERSON>-Crush-Game)                     | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/01-<PERSON>-Crush-Game/)           |
| 02  | [Archery Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/02-Archery-Game)                             | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/02-Archery-Game/)               |
| 03  | [Speed Typing Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/03-Speed-Typing-Game)                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/03-Speed-Typing-Game/)          |
| 04  | [Breakout Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/04-Breakout-Game)                           | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/04-Breakout-Game/)              |
| 05  | [Minesweeper Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/05-Minesweeper-Game)                     | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/05-Minesweeper-Game/)           |
| 06  | [Tower Blocks Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/06-Tower-Blocks)                        | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/06-Tower-Blocks/)               |
| 07  | [Ping Pong Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/07-Ping-Pong-Game)                         | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/07-Ping-Pong-Game/)             |
| 08  | [Tetris Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/08-Tetris-Game)                               | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/08-Tetris-Game/)                |
| 09  | [Tilting Maze Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/09-Tilting-Maze-Game)                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/09-Tilting-Maze-Game/)          |
| 10  | [Memory Card Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/10-Memory-Card-Game)                     | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/10-Memory-Card-Game/)           |
| 11  | [Rock Paper Scissors Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/11-Rock-Paper-Scissors)          | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/11-Rock-Paper-Scissors/)        |
| 12  | [Type Number Guessing Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/12-Type-Number-Guessing-Game)   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/12-Type-Number-Guessing-Game/)  |
| 13  | [Tic Tac Toe Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/13-Tic-Tac-Toe)                          | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/13-Tic-Tac-Toe/)                |
| 14  | [Snake Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/14-Snake-Game)                                 | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/14-Snake-Game/)                 |
| 15  | [Connect Four Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/15-Connect-Four-Game)                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/15-Connect-Four-Game/)          |
| 16  | [Insect Catch Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/16-Insect-Catch-Game)                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/16-Insect-Catch-Game/)          |
| 17  | [Typing Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/17-Typing-Game)                               | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/17-Typing-Game/)                |
| 18  | [Hangman Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/18-Hangman-Game)                             | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/18-Hangman-Game/)               |
| 19  | [Flappy Bird Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/19-Flappy-Bird-Game)                     | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/19-Flappy-Bird-Game/)           |
| 20  | [Crossy Road Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/20-Crossy-Road-Game)                     | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/20-Crossy-Road-Game/)           |
| 21  | [2048 Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/21-2048-Game)                                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/21-2048-Game/)                  |
| 22  | [Dice Roll Simulator](https://github.com/he-is-talha/html-css-javascript-games/tree/main/22-Dice-Roll-Simulator)               | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/22-Dice-Roll-Simulator/)        |
| 23  | [Shape Clicker Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/23-Shape-Clicker-Game)                 | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/23-Shape-Clicker-Game/)         |
| 24  | [Typing Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/24-Typing-Game)                               | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/24-Typing-Game/)                |
| 25  | [Speak Number Guessing Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/25-Speak-Number-Guessing-Game) | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/25-Speak-Number-Guessing-Game/) |
| 26  | [Fruit Slicer Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/26-Fruit-Slicer-Game)                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/26-Fruit-Slicer-Game/)          |
| 27  | [Quiz Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/27-Quiz-Game)                                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/27-Quiz-Game/)                  |
| 28  | [Emoji Catcher Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/28-Emoji-Catcher-Game)                 | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/28-Emoji-Catcher-Game/)         |
| 29  | [Whack A Mole Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/29-Whack-A-Mole-Game)                   | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/29-Whack-A-Mole-Game/)          |
| 30  | [Simon Says Game](https://github.com/he-is-talha/html-css-javascript-games/tree/main/30-Simon-Says-Game)                       | [Live Demo](https://he-is-talha.github.io/html-css-javascript-games/30-Simon-Says-Game/)            |

## Games Description

1. **Candy Crush Game**: Enjoy the classic match-three puzzle game where you swap colored candies to create matches and clear levels. 🍬🍭
2. **Archery Game**: Test your aim in this challenging archery game where precision and timing are key to hitting the bullseye. 🏹
3. **Speed Typing Game**: Improve your typing speed and accuracy with this game that challenges you to type words quickly under time pressure. ⌨️
4. **Breakout Game**: Relive the arcade classic where you use a paddle to bounce a ball and break bricks, aiming for a high score. 🎮
5. **Minesweeper Game**: Exercise your logical thinking with this puzzle game where you avoid hidden mines and uncover safe tiles. 💣
6. **Tower Blocks**: Build a tower by stacking blocks as high as possible without letting them topple over in this physics-based game. 🏗️
7. **Ping Pong Game**: Experience the thrill of table tennis as you compete against an AI opponent in this classic sports game. 🏓
8. **Tetris Game**: Arrange falling tetrominoes to create complete lines and score points in this addictive puzzle game. 🧱
9. **Tilting Maze Game**: Navigate a ball through a tilting maze, avoiding traps and reaching the goal in this challenging game of skill. 🌀
10. **Memory Card Game**: Test your memory by matching pairs of cards in this classic concentration game. 🃏
11. **Rock Paper Scissors**: Play the timeless hand game against the computer and see who comes out victorious. ✂️
12. **Type Number Guessing Game**: Guess the hidden number based on clues provided after each guess in this number guessing game. 🔢
13. **Tic Tac Toe**: Challenge a friend or AI in this simple yet strategic game of placing Xs and Os in a 3x3 grid. ⭕❌
14. **Snake Game**: Control a growing snake, eat food, and avoid collisions with walls and your own tail in this nostalgic arcade game. 🐍
15. **Connect Four Game**: Strategically drop colored discs to connect four in a row vertically, horizontally, or diagonally in this classic board game. 🔵🔴
16. **Insect Catch Game**: Test your reflexes by clicking on randomly appearing insects to catch them before they disappear. 🐞
17. **Typing Game**: Sharpen your typing skills by typing specific words or sentences as quickly and accurately as possible. ⌨️
18. **Hangman Game**: Guess the hidden word by suggesting letters within a certain number of guesses in this word-guessing game. 🎩
19. **Flappy Bird Game**: Guide a bird through gaps in pipes by tapping to flap its wings, avoiding obstacles and aiming for a high score. 🐦
20. **Crossy Road Game**: Help a character cross roads, rivers, and other obstacles in this endless hopping game with a retro arcade feel. 🚦
21. **2048 Game**: Slide numbered tiles on a grid to combine them and create a tile with the number 2048 in this addictive puzzle game. 🧩
22. **Dice Roll Simulator**: Simulate rolling dice to achieve different combinations or outcomes in this virtual dice rolling game. 🎲
23. **Shape Clicker Game**: Click on various shapes appearing on the screen within a time limit to score points in this clicker game. 🔷🔶
24. **Typing Game**: Improve your typing speed and accuracy by typing specific words or sentences under time pressure. ⌨️
25. **Speak Number Guessing Game**: Guess the hidden number by speaking your guesses aloud in this voice-activated number guessing game. 🗣️🔢
26. **Fruit Slicer Game**: Swipe across the screen to slice falling fruits while avoiding bombs in this fast-paced fruit-slicing game. 🍉🔪
27. **Quiz Game**: Test your knowledge on various topics by answering trivia questions and aiming for a high score in this quiz game. 🧠📚
28. **Emoji Catcher Game**: Catch falling emojis with a basket or container while avoiding bombs and other obstacles in this emoji-catching game. 🎯😄
29. **Whack-a-Mole Game**: Test your reaction speed by hitting randomly appearing moles with a mallet before they disappear in this classic arcade game. 🕹️
30. **Simon Says Game**: Follow the color sequence and repeat it correctly to advance through levels. Test your memory and speed in this classic memory game! 🎮💡

## License

The MIT License

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
