<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Talha - Fruit Slicer Game</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="style.css" />
    <link
      rel="stylesheet"
      href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css"
    />
    <link rel="icon" href="https://i.ibb.co/M6KTWnf/pic.jpg" />

    <!-- 👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻-->
    <!-- Also uploaded the demo of this code in a gif : https://c.tenor.com/x8v1oNUOmg4AAAAd/tenor.gif-->
    <!-- 👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻-->

    <!-- 👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻👇🏻-->
    <!-- More html-css-js Games Calculators Games Cards Elements Projects on https://www.github.com/he-is-talha -->
    <!-- 👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻👆🏻-->
  </head>
  <body>
    <div id="container">
      <div id="instructions">
        Slice Fruits
      </div>
      <div id="fruitcontainer">
        <div id="score">
          <div class="scoreDisplay">
            <img
              src="https://raw.githubusercontent.com/Saumya-07/Fruit-Slicer/master/images/scr.png"
              alt=""
            />
            <span id="scoreValue">0</span>
          </div>
        </div>
        <div id="trialsleft"></div>
        <div id="front">
          Are you ready for the ultimate slice action ? <br />
          <img
            src="https://raw.githubusercontent.com/Saumya-07/Fruit-Slicer/master/images/splash.png"
            alt="Fruit logo"
          />
        </div>
        <img id="fruit1" class="fruit" />
      </div>
      <div id="startReset">
        Start Game
      </div>
      <!--for game over block-->
      <div id="gameOver"></div>
    </div>
    <!--for audio files-->
    <audio id="slicesound">
      <source
        src="https://github.com/Saumya-07/Fruit-Slicer/blob/master/audio/slicefruit.mp3"
      />
      <source
        src="https://github.com/Saumya-07/Fruit-Slicer/blob/master/audio/slicefruit.ogg"
      />
      <source
        src="https://github.com/Saumya-07/Fruit-Slicer/blob/master/audio/slicefruit.wav"
      />
    </audio>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.4/jquery-ui.min.js"></script>
    <script src="script.js"></script>
  </body>
</html>
